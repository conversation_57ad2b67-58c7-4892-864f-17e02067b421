2025-10-10 00:04:17.591 | spring.profiles.active_IS_UNDEFINED | evcard-mtc | ReconcileService RUNNING | WARN  | org.apache.curator.retry.ExponentialBackoffRetry |  | ExponentialBackoffRetry.java:74 | Sleep extension too large (4000). Pinning to 3000
2025-10-10 00:04:17.591 | spring.profiles.active_IS_UNDEFINED | evcard-mtc | ReconcileService RUNNING | WARN  | org.apache.curator.retry.ExponentialBackoffRetry |  | ExponentialBackoffRetry.java:74 | Sleep extension too large (4000). Pinning to 3000
2025-10-10 00:04:17.591 | spring.profiles.active_IS_UNDEFINED | evcard-mtc | ReconcileService RUNNING | WARN  | org.apache.curator.retry.ExponentialBackoffRetry |  | ExponentialBackoffRetry.java:74 | Sleep extension too large (5000). Pinning to 3000
2025-10-10 00:04:17.591 | spring.profiles.active_IS_UNDEFINED | evcard-mtc | ReconcileService RUNNING | WARN  | org.apache.curator.retry.ExponentialBackoffRetry |  | ExponentialBackoffRetry.java:74 | Sleep extension too large (6000). Pinning to 3000
2025-10-10 00:04:17.801 | spring.profiles.active_IS_UNDEFINED | evcard-mtc | redisson-netty-3-9 | INFO  | org.redisson.connection.DNSMonitor |  | DNSMonitor.java:147 | Detected DNS change. Master redis://evcard-st-lan.redis.rds.aliyuncs.com:6379 has changed ip from *********** to ***********
2025-10-10 00:04:20.908 | spring.profiles.active_IS_UNDEFINED | evcard-mtc | redisson-netty-3-15 | ERROR | org.redisson.connection.SingleEntry |  | MasterSlaveEntry.java:533 | Unable to change master from: evcard-st-lan.redis.rds.aliyuncs.com/***********:6379 to: redis://evcard-st-lan.redis.rds.aliyuncs.com:6379
java.util.concurrent.CompletionException: org.redisson.client.RedisConnectionException: Unable to connect to Redis server: evcard-st-lan.redis.rds.aliyuncs.com/***********:6379
	at org.redisson.connection.ConnectionsHolder.lambda$createConnection$2(ConnectionsHolder.java:166)
	at java.util.concurrent.CompletableFuture.uniHandle(CompletableFuture.java:836)
	at java.util.concurrent.CompletableFuture$UniHandle.tryFire(CompletableFuture.java:811)
	at java.util.concurrent.CompletableFuture.postComplete(CompletableFuture.java:488)
	at java.util.concurrent.CompletableFuture.completeExceptionally(CompletableFuture.java:1990)
	at org.redisson.connection.ConnectionsHolder.lambda$createConnection$5(ConnectionsHolder.java:179)
	at java.util.concurrent.CompletableFuture.uniWhenComplete(CompletableFuture.java:774)
	at java.util.concurrent.CompletableFuture$UniWhenComplete.tryFire(CompletableFuture.java:750)
	at java.util.concurrent.CompletableFuture.postComplete(CompletableFuture.java:488)
	at java.util.concurrent.CompletableFuture.completeExceptionally(CompletableFuture.java:1990)
	at org.redisson.client.RedisClient$1$1.run(RedisClient.java:295)
	at io.netty.util.concurrent.AbstractEventExecutor.safeExecute(AbstractEventExecutor.java:164)
	at io.netty.util.concurrent.SingleThreadEventExecutor.runAllTasks(SingleThreadEventExecutor.java:472)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:500)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:989)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:750)
Caused by: org.redisson.client.RedisConnectionException: Unable to connect to Redis server: evcard-st-lan.redis.rds.aliyuncs.com/***********:6379
	at org.redisson.connection.ConnectionsHolder.lambda$createConnection$2(ConnectionsHolder.java:165)
	... 17 common frames omitted
Caused by: java.util.concurrent.CompletionException: org.redisson.client.RedisTimeoutException: Command execution timeout for command: (AUTH), params: (password masked), Redis client: [addr=redis://evcard-st-lan.redis.rds.aliyuncs.com:6379]
	at java.util.concurrent.CompletableFuture.encodeRelay(CompletableFuture.java:326)
	at java.util.concurrent.CompletableFuture.completeRelay(CompletableFuture.java:338)
	at java.util.concurrent.CompletableFuture.uniRelay(CompletableFuture.java:925)
	at java.util.concurrent.CompletableFuture$UniRelay.tryFire(CompletableFuture.java:913)
	at java.util.concurrent.CompletableFuture.postComplete(CompletableFuture.java:488)
	at java.util.concurrent.CompletableFuture.completeExceptionally(CompletableFuture.java:1990)
	at org.redisson.client.RedisConnection.lambda$async$0(RedisConnection.java:254)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.expire(HashedWheelTimer.java:669)
	at io.netty.util.HashedWheelTimer$HashedWheelBucket.expireTimeouts(HashedWheelTimer.java:744)
	at io.netty.util.HashedWheelTimer$Worker.run(HashedWheelTimer.java:469)
	... 2 common frames omitted
Caused by: org.redisson.client.RedisTimeoutException: Command execution timeout for command: (AUTH), params: (password masked), Redis client: [addr=redis://evcard-st-lan.redis.rds.aliyuncs.com:6379]
	at org.redisson.client.RedisConnection.lambda$async$0(RedisConnection.java:253)
	... 5 common frames omitted
2025-10-10 00:04:26.001 | spring.profiles.active_IS_UNDEFINED | evcard-mtc | redisson-netty-3-18 | INFO  | org.redisson.connection.DNSMonitor |  | DNSMonitor.java:147 | Detected DNS change. Master redis://evcard-st-lan.redis.rds.aliyuncs.com:6379 has changed ip from *********** to ***********

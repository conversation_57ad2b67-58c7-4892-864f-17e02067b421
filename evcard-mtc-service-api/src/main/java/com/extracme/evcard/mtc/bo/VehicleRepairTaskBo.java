package com.extracme.evcard.mtc.bo;

import lombok.Data;

@Data
public class VehicleRepairTaskBo {

    private Long id;
    private String taskNo;
    private String vehicleNo;
    private String vin;
    private Integer repairType; // 维修类型（ 1：事故维修 2：自费维修 3：车辆保养  7：终端维修 9：短租包修（外观类） 10：短租包修（一般类））
    private Integer repairTaskStatus; // 任任务状态 1-待分配 2-送修中 3-待接车 4-进保预审 5-维修报价 6-核损核价 7-车辆维修 8-改派申请 9-车辆验收 10-已完成 11-已关闭
    private Integer leavingFactory; // 出厂登记 (1-已登记  2-未登记 3-已关闭)
    private Integer reviewToSelFeeFlag;  // 事故转自费(0否 1是) 默认是-1
    private Integer repairDepotType; // 修理厂类型 (1:合作 2:非合作)
    private String repairDepotName; // 修理厂名称
    private String vehicleModelName; // 车型名称
    private String orgName; // 所属公司
    private String createTime; // 创建时间
    private String declareNo;//关联账单
    private Integer declareStatus; //账单状态 1=未提交 2=审批中 3=审批通过 4=审批拒绝
    private String settlementNo; //关联结算单 即GR号
    private Integer settlementStatus; //关联结算单状态 1=未提交 2=审核中 3=审批通过 4=审批拒绝 5=已作废 6=已关闭
    private Integer declareSettlementStatus;// 账单状态 1=已生成 2=待结算 3=结算中 4=已结算 5=已作废'
    private String repairGrade; // 修理厂等级
    private String vehicleOrgId; // 车辆公司
    private String vehicleOrgName;
    private String vehicleOperateOrgName; // 车辆运营
    private String vehicleOperateOrgId;
}

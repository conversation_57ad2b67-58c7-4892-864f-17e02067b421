<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.extracme.evcard.mtc.dao.MtcRepairTaskExtraDurationDetailMapper" >
  <resultMap id="BaseResultMap" type="com.extracme.evcard.mtc.model.MtcRepairTaskExtraDurationDetail" >
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Mon Sep 27 14:16:46 CST 2021.
    -->
    <id column="id" property="id" jdbcType="BIGINT" />
    <result column="task_no" property="taskNo" jdbcType="VARCHAR" />
    <result column="duration_type" property="durationType" jdbcType="INTEGER" />
    <result column="duration" property="duration" jdbcType="BIGINT" />
    <result column="status" property="status" jdbcType="DECIMAL" />
    <result column="remark" property="remark" jdbcType="VARCHAR" />
    <result column="create_time" property="createTime" jdbcType="TIMESTAMP" />
    <result column="create_oper_id" property="createOperId" jdbcType="BIGINT" />
    <result column="create_oper_name" property="createOperName" jdbcType="VARCHAR" />
    <result column="update_time" property="updateTime" jdbcType="TIMESTAMP" />
    <result column="update_oper_id" property="updateOperId" jdbcType="BIGINT" />
    <result column="update_oper_name" property="updateOperName" jdbcType="VARCHAR" />
  </resultMap>
  <sql id="Example_Where_Clause" >
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Mon Sep 27 14:16:46 CST 2021.
    -->
    <where >
      <foreach collection="oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause" >
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Mon Sep 27 14:16:46 CST 2021.
    -->
    <where >
      <foreach collection="example.oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List" >
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Mon Sep 27 14:16:46 CST 2021.
    -->
    id, task_no, duration_type, duration, status, remark, create_time, create_oper_id, 
    create_oper_name, update_time, update_oper_id, update_oper_name
  </sql>
  <select id="selectByExample" resultMap="BaseResultMap" parameterType="com.extracme.evcard.mtc.model.MtcRepairTaskExtraDurationDetailExample" >
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Mon Sep 27 14:16:46 CST 2021.
    -->
    select
    <if test="distinct" >
      distinct
    </if>
    'true' as QUERYID,
    <include refid="Base_Column_List" />
    from ${mtcSchema}.mtc_repair_task_extra_duration_detail
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null" >
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Long" >
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Mon Sep 27 14:16:46 CST 2021.
    -->
    select 
    <include refid="Base_Column_List" />
    from ${mtcSchema}.mtc_repair_task_extra_duration_detail
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long" >
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Mon Sep 27 14:16:46 CST 2021.
    -->
    delete from ${mtcSchema}.mtc_repair_task_extra_duration_detail
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.extracme.evcard.mtc.model.MtcRepairTaskExtraDurationDetailExample" >
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Mon Sep 27 14:16:46 CST 2021.
    -->
    delete from ${mtcSchema}.mtc_repair_task_extra_duration_detail
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.extracme.evcard.mtc.model.MtcRepairTaskExtraDurationDetail" >
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Mon Sep 27 14:16:46 CST 2021.
    -->
    insert into ${mtcSchema}.mtc_repair_task_extra_duration_detail (id, task_no, duration_type, 
      duration, status, remark, 
      create_time, create_oper_id, create_oper_name, 
      update_time, update_oper_id, update_oper_name
      )
    values (#{id,jdbcType=BIGINT}, #{taskNo,jdbcType=VARCHAR}, #{durationType,jdbcType=INTEGER}, 
      #{duration,jdbcType=BIGINT}, #{status,jdbcType=DECIMAL}, #{remark,jdbcType=VARCHAR}, 
      #{createTime,jdbcType=TIMESTAMP}, #{createOperId,jdbcType=BIGINT}, #{createOperName,jdbcType=VARCHAR}, 
      #{updateTime,jdbcType=TIMESTAMP}, #{updateOperId,jdbcType=BIGINT}, #{updateOperName,jdbcType=VARCHAR}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.extracme.evcard.mtc.model.MtcRepairTaskExtraDurationDetail" >
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Mon Sep 27 14:16:46 CST 2021.
    -->
    insert into ${mtcSchema}.mtc_repair_task_extra_duration_detail
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        id,
      </if>
      <if test="taskNo != null" >
        task_no,
      </if>
      <if test="durationType != null" >
        duration_type,
      </if>
      <if test="duration != null" >
        duration,
      </if>
      <if test="status != null" >
        status,
      </if>
      <if test="remark != null" >
        remark,
      </if>
      <if test="createTime != null" >
        create_time,
      </if>
      <if test="createOperId != null" >
        create_oper_id,
      </if>
      <if test="createOperName != null" >
        create_oper_name,
      </if>
      <if test="updateTime != null" >
        update_time,
      </if>
      <if test="updateOperId != null" >
        update_oper_id,
      </if>
      <if test="updateOperName != null" >
        update_oper_name,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        #{id,jdbcType=BIGINT},
      </if>
      <if test="taskNo != null" >
        #{taskNo,jdbcType=VARCHAR},
      </if>
      <if test="durationType != null" >
        #{durationType,jdbcType=INTEGER},
      </if>
      <if test="duration != null" >
        #{duration,jdbcType=BIGINT},
      </if>
      <if test="status != null" >
        #{status,jdbcType=DECIMAL},
      </if>
      <if test="remark != null" >
        #{remark,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null" >
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createOperId != null" >
        #{createOperId,jdbcType=BIGINT},
      </if>
      <if test="createOperName != null" >
        #{createOperName,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null" >
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateOperId != null" >
        #{updateOperId,jdbcType=BIGINT},
      </if>
      <if test="updateOperName != null" >
        #{updateOperName,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.extracme.evcard.mtc.model.MtcRepairTaskExtraDurationDetailExample" resultType="java.lang.Integer" >
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Mon Sep 27 14:16:46 CST 2021.
    -->
    select count(*) from ${mtcSchema}.mtc_repair_task_extra_duration_detail
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map" >
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Mon Sep 27 14:16:46 CST 2021.
    -->
    update ${mtcSchema}.mtc_repair_task_extra_duration_detail
    <set >
      <if test="record.id != null" >
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.taskNo != null" >
        task_no = #{record.taskNo,jdbcType=VARCHAR},
      </if>
      <if test="record.durationType != null" >
        duration_type = #{record.durationType,jdbcType=INTEGER},
      </if>
      <if test="record.duration != null" >
        duration = #{record.duration,jdbcType=BIGINT},
      </if>
      <if test="record.status != null" >
        status = #{record.status,jdbcType=DECIMAL},
      </if>
      <if test="record.remark != null" >
        remark = #{record.remark,jdbcType=VARCHAR},
      </if>
      <if test="record.createTime != null" >
        create_time = #{record.createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.createOperId != null" >
        create_oper_id = #{record.createOperId,jdbcType=BIGINT},
      </if>
      <if test="record.createOperName != null" >
        create_oper_name = #{record.createOperName,jdbcType=VARCHAR},
      </if>
      <if test="record.updateTime != null" >
        update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.updateOperId != null" >
        update_oper_id = #{record.updateOperId,jdbcType=BIGINT},
      </if>
      <if test="record.updateOperName != null" >
        update_oper_name = #{record.updateOperName,jdbcType=VARCHAR},
      </if>
    </set>
    <if test="_parameter != null" >
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map" >
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Mon Sep 27 14:16:46 CST 2021.
    -->
    update ${mtcSchema}.mtc_repair_task_extra_duration_detail
    set id = #{record.id,jdbcType=BIGINT},
      task_no = #{record.taskNo,jdbcType=VARCHAR},
      duration_type = #{record.durationType,jdbcType=INTEGER},
      duration = #{record.duration,jdbcType=BIGINT},
      status = #{record.status,jdbcType=DECIMAL},
      remark = #{record.remark,jdbcType=VARCHAR},
      create_time = #{record.createTime,jdbcType=TIMESTAMP},
      create_oper_id = #{record.createOperId,jdbcType=BIGINT},
      create_oper_name = #{record.createOperName,jdbcType=VARCHAR},
      update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      update_oper_id = #{record.updateOperId,jdbcType=BIGINT},
      update_oper_name = #{record.updateOperName,jdbcType=VARCHAR}
    <if test="_parameter != null" >
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.extracme.evcard.mtc.model.MtcRepairTaskExtraDurationDetail" >
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Mon Sep 27 14:16:46 CST 2021.
    -->
    update ${mtcSchema}.mtc_repair_task_extra_duration_detail
    <set >
      <if test="taskNo != null" >
        task_no = #{taskNo,jdbcType=VARCHAR},
      </if>
      <if test="durationType != null" >
        duration_type = #{durationType,jdbcType=INTEGER},
      </if>
      <if test="duration != null" >
        duration = #{duration,jdbcType=BIGINT},
      </if>
      <if test="status != null" >
        status = #{status,jdbcType=DECIMAL},
      </if>
      <if test="remark != null" >
        remark = #{remark,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null" >
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createOperId != null" >
        create_oper_id = #{createOperId,jdbcType=BIGINT},
      </if>
      <if test="createOperName != null" >
        create_oper_name = #{createOperName,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null" >
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateOperId != null" >
        update_oper_id = #{updateOperId,jdbcType=BIGINT},
      </if>
      <if test="updateOperName != null" >
        update_oper_name = #{updateOperName,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.extracme.evcard.mtc.model.MtcRepairTaskExtraDurationDetail" >
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Mon Sep 27 14:16:46 CST 2021.
    -->
    update ${mtcSchema}.mtc_repair_task_extra_duration_detail
    set task_no = #{taskNo,jdbcType=VARCHAR},
      duration_type = #{durationType,jdbcType=INTEGER},
      duration = #{duration,jdbcType=BIGINT},
      status = #{status,jdbcType=DECIMAL},
      remark = #{remark,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      create_oper_id = #{createOperId,jdbcType=BIGINT},
      create_oper_name = #{createOperName,jdbcType=VARCHAR},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      update_oper_id = #{updateOperId,jdbcType=BIGINT},
      update_oper_name = #{updateOperName,jdbcType=VARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>

  <resultMap id="RepairTaskEfficiencyResultMap" type="com.extracme.evcard.mtc.bo.QueryRepairTaskEfficiencyResultBO">
    <id column="id" property="id" jdbcType="BIGINT" />
    <result column="task_no" property="taskNo" jdbcType="VARCHAR" />
    <result column="repair_type_id" property="repairTypeId" jdbcType="INTEGER"/>
    <result column="current_tache" property="currentTache" jdbcType="BIGINT"/>
    <result column="vehicle_no" property="vehicleNo" jdbcType="VARCHAR"/>
    <result column="repair_depot_name" property="repairDepotName" jdbcType="VARCHAR"/>
    <result column="org_name" property="orgName" jdbcType="VARCHAR"/>
    <result column="task_inflow_time" property="taskInflowTime" jdbcType="TIMESTAMP"/>
    <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
    <result column="vehicle_recive_time" property="vehicleReciveTime" jdbcType="TIMESTAMP"/>
    <result column="vehicle_check_time" property="vehicleCheckTime" jdbcType="TIMESTAMP"/>
    <result column="vehicle_insurance_total_amount" property="vehicleInsuranceTotalAmount" jdbcType="DECIMAL"/>
    <result column="review_to_sel_fee_flag" property="reviewToSelFeeFlag" jdbcType="INTEGER"/>
    <result column="vehicleReceiveDuration" property="vehicleReceiveDuration" jdbcType="DECIMAL"/>
    <result column="insuranceQuoteDuration" property="insuranceQuoteDuration" jdbcType="DECIMAL"/>
    <result column="vehicleMaintenanceLossAuditDuration" property="vehicleMaintenanceLossAuditDuration" jdbcType="DECIMAL"/>
    <result column="vehicleMaintenanceDuration" property="vehicleMaintenanceDuration" jdbcType="DECIMAL"/>
    <result column="vehicleCheckDuration" property="vehicleCheckDuration" jdbcType="DECIMAL"/>
    <result column="totalDuration" property="totalDuration" jdbcType="DECIMAL"/>
    <result column="costDuration" property="costDuration" jdbcType="DECIMAL"/>
    <result column="insurancePreReviewDuration" property="insurancePreReviewDuration" jdbcType="DECIMAL"/>

    <!-- 维修任务进度 -->
    <result column="examine_level" jdbcType="INTEGER" property="examineLevel"/>
    <result column="vehicle_transfer_task_schedule" jdbcType="BIGINT" property="vehicleTransferTaskSchedule" />
    <result column="insurance_quote_task_schedule" jdbcType="BIGINT" property="insuranceQuoteTaskSchedule" />
    <result column="verification_loss_task_schedule" jdbcType="BIGINT" property="verificationLossTaskSchedule" />
    <result column="reassignment_task_schedule" jdbcType="BIGINT" property="reassignmentTaskSchedule" />
    <result column="vehicle_repair_task_schedule" jdbcType="BIGINT" property="vehicleRepairTaskSchedule" />
    <result column="vehicle_check_task_schedule" jdbcType="BIGINT" property="vehicleCheckTaskSchedule" />
    <result column="insurance_pre_review_task_schedule" jdbcType="BIGINT" property="insurancePreReviewTaskSchedule" />

    <result column="material_collection_task_schedule" jdbcType="INTEGER" property="materialCollectionTaskSchedule" javaType="java.lang.Long"/>
    <result column="loss_registration_task_schedule" jdbcType="INTEGER" property="lossRegistrationTaskSchedule" javaType="java.lang.Long"/>
    <result column="settlement_task_schedule" jdbcType="INTEGER" property="settlementTaskSchedule" javaType="java.lang.Long"/>
  </resultMap>

  <select id="queryRepairTaskEfficiency" parameterType="com.extracme.evcard.mtc.bo.QueryRepairTaskEfficiencyBO"
          resultMap="RepairTaskEfficiencyResultMap">
    SELECT
      t1.id,
      t1.task_no,
      t1.repair_type_id,
      t1.current_tache,
      t1.examine_level,
      t1.vehicle_transfer_task_schedule,
      t1.insurance_quote_task_schedule,
      t1.verification_loss_task_schedule,
      t1.reassignment_task_schedule,
      t1.vehicle_repair_task_schedule,
      t1.vehicle_check_task_schedule,
      t1.material_collection_task_schedule,
      t1.loss_registration_task_schedule,
      t1.settlement_task_schedule,
      t1.insurance_pre_review_task_schedule,
      t1.vehicle_no,
      t1.repair_depot_name,
      t1.org_name,
      t1.task_inflow_time,
      t1.create_time,
      t1.vehicle_recive_time,
      t1.vehicle_check_time,
      t1.vehicle_insurance_total_amount,
      t1.review_to_sel_fee_flag,
      t2.duration as vehicleReceiveDuration,
      t3.duration as insuranceQuoteDuration,
      t4.duration as vehicleMaintenanceLossAuditDuration,
      t5.duration as vehicleMaintenanceDuration,
      t6.duration as vehicleCheckDuration,
      t7.duration as totalDuration,
      t8.duration as costDuration,
      t9.duration as insurancePreReviewDuration
    FROM
    ${mtcSchema}.mtc_repair_task t1
    LEFT JOIN ${mtcSchema}.mtc_repair_task_extra_duration_detail t2 ON t1.task_no = t2.task_no AND t2.duration_type = 1
    LEFT JOIN ${mtcSchema}.mtc_repair_task_extra_duration_detail t3 ON T1.task_no = t3.task_no AND t3.duration_type = 2
    LEFT JOIN ${mtcSchema}.mtc_repair_task_extra_duration_detail t4 ON T1.task_no = t4.task_no AND t4.duration_type = 3
    LEFT JOIN ${mtcSchema}.mtc_repair_task_extra_duration_detail t5 ON T1.task_no = t5.task_no AND t5.duration_type = 4
    LEFT JOIN ${mtcSchema}.mtc_repair_task_extra_duration_detail t6 ON T1.task_no = t6.task_no AND t6.duration_type = 5
    LEFT JOIN ${mtcSchema}.mtc_repair_task_extra_duration_detail t7 ON T1.task_no = t7.task_no AND t7.duration_type = 6
    LEFT JOIN ${mtcSchema}.mtc_repair_task_extra_duration_detail t8 ON T1.task_no = t8.task_no AND t8.duration_type = 7
    LEFT JOIN ${mtcSchema}.mtc_repair_task_extra_duration_detail t9 ON T1.task_no = t9.task_no AND t9.duration_type = 8
    <where>
      <if test="orgId != null and orgId != ''">
        AND t1.org_id like concat(#{orgId},'%')
      </if>
      <if test="repairTypeId != null">
        AND t1.repair_type_id = #{repairTypeId,jdbcType=INTEGER}
      </if>
      <if test="repairTypeId == null">
        AND t1.repair_type_id in (1, 2, 3, 6, 7, 9, 10)
      </if>
      <if test="currentTacheList != null and currentTacheList.size() > 0" >
        AND t1.current_tache in
        <foreach item="item" index="index" collection="currentTacheList" open="(" separator="," close=")">
            #{item}
        </foreach>
      </if>
      <if test="repairDepotName != null and repairDepotName != ''">
        and t1.repair_depot_name like concat('%',#{repairDepotName, jdbcType=VARCHAR},'%')
      </if>
      <if test="taskNo != null and taskNo != ''">
        and t1.task_no = #{taskNo,jdbcType=VARCHAR}
      </if>
      <if test="vehicleNo != null and vehicleNo != ''">
        and t1.vehicle_no like concat(#{vehicleNo,jdbcType=VARCHAR},'%')
      </if>
      <if test="vin != null and vin != ''">
        and t1.vin = #{vin,jdbcType=VARCHAR}
      </if>
      <if test="taskCreateTimeStart != null">
        and t1.task_inflow_time &gt;= #{taskCreateTimeStart, jdbcType = TIMESTAMP}
      </if>
      <if test="taskCreateTimeEnd != null">
        and t1.task_inflow_time &lt;= #{taskCreateTimeEnd, jdbcType = TIMESTAMP}
      </if>
      <if test="taskFlowTimeStart != null">
        and t1.create_time &gt;= #{taskFlowTimeStart, jdbcType = TIMESTAMP}
      </if>
      <if test="taskFlowTimeEnd != null">
        and t1.create_time &lt;= #{taskFlowTimeEnd, jdbcType = TIMESTAMP}
      </if>
      <if test="vehicleReceiveTimeStart != null">
        and t1.vehicle_recive_time &gt;= #{vehicleReceiveTimeStart, jdbcType = TIMESTAMP}
      </if>
      <if test="vehicleReceiveTimeEnd != null">
        and t1.vehicle_recive_time &lt;= #{vehicleReceiveTimeEnd, jdbcType = TIMESTAMP}
      </if>
      <if test="vehicleCheckTimeStart != null">
        and t1.vehicle_check_time &gt;= #{vehicleCheckTimeStart, jdbcType = TIMESTAMP}
      </if>
      <if test="vehicleCheckTimeEnd != null">
        and t1.vehicle_check_time &lt;= #{vehicleCheckTimeEnd, jdbcType = TIMESTAMP}
      </if>
      <!-- 根据有无存储车辆验收时长判断任务是否完成 -->
      <if test="taskFinish != null and taskFinish == 0">
        and t6.duration is null
      </if>
      <if test="taskFinish != null and taskFinish == 1">
        and t6.duration is not null
      </if>
      <if test="reviewToSelFeeFlag != null">
        AND t1.review_to_sel_fee_flag = #{reviewToSelFeeFlag}
      </if>
    </where>
    order by t1.create_time desc
  </select>
  
  <select id="queryRepairTaskEfficiencyCount" parameterType="com.extracme.evcard.mtc.bo.QueryRepairTaskEfficiencyBO"
          resultType="java.lang.Long">
    SELECT
      count(*)
    FROM
      ${mtcSchema}.mtc_repair_task t1
    LEFT JOIN ${mtcSchema}.mtc_repair_task_extra_duration_detail t2 ON t1.task_no = t2.task_no AND t2.duration_type = 1
    LEFT JOIN ${mtcSchema}.mtc_repair_task_extra_duration_detail t3 ON T1.task_no = t3.task_no AND t3.duration_type = 2
    LEFT JOIN ${mtcSchema}.mtc_repair_task_extra_duration_detail t4 ON T1.task_no = t4.task_no AND t4.duration_type = 3
    LEFT JOIN ${mtcSchema}.mtc_repair_task_extra_duration_detail t5 ON T1.task_no = t5.task_no AND t5.duration_type = 4
    LEFT JOIN ${mtcSchema}.mtc_repair_task_extra_duration_detail t6 ON T1.task_no = t6.task_no AND t6.duration_type = 5
    LEFT JOIN ${mtcSchema}.mtc_repair_task_extra_duration_detail t7 ON T1.task_no = t7.task_no AND t7.duration_type = 6
    LEFT JOIN ${mtcSchema}.mtc_repair_task_extra_duration_detail t8 ON T1.task_no = t8.task_no AND t8.duration_type = 7
    LEFT JOIN ${mtcSchema}.mtc_repair_task_extra_duration_detail t9 ON T1.task_no = t9.task_no AND t9.duration_type = 8
    <where>
      <if test="orgId != null and orgId != ''">
        AND t1.org_id like concat(#{orgId},'%')
      </if>
      <if test="repairTypeId != null">
        AND t1.repair_type_id = #{repairTypeId,jdbcType=INTEGER}
      </if>
      <if test="repairTypeId == null">
        AND t1.repair_type_id in (1, 2, 3, 6, 7, 9, 10)
      </if>
      <if test="currentTacheList != null and currentTacheList.size() > 0" >
        AND t1.current_tache in
        <foreach item="item" index="index" collection="currentTacheList" open="(" separator="," close=")">
          #{item}
        </foreach>
      </if>
      <if test="repairDepotName != null and repairDepotName != ''">
        and t1.repair_depot_name like concat('%',#{repairDepotName, jdbcType=VARCHAR},'%')
      </if>
      <if test="taskNo != null and taskNo != ''">
        and t1.task_no = #{taskNo,jdbcType=VARCHAR}
      </if>
      <if test="vehicleNo != null and vehicleNo != ''">
        and t1.vehicle_no like concat(#{vehicleNo,jdbcType=VARCHAR},'%')
      </if>
      <if test="vin != null and vin != ''">
        and t1.vin = #{vin,jdbcType=VARCHAR}
      </if>
      <if test="taskCreateTimeStart != null">
        and t1.task_inflow_time &gt;= #{taskCreateTimeStart, jdbcType = TIMESTAMP}
      </if>
      <if test="taskCreateTimeEnd != null">
        and t1.task_inflow_time &lt;= #{taskCreateTimeEnd, jdbcType = TIMESTAMP}
      </if>
      <if test="taskFlowTimeStart != null">
        and t1.create_time &gt;= #{taskFlowTimeStart, jdbcType = TIMESTAMP}
      </if>
      <if test="taskFlowTimeEnd != null">
        and t1.create_time &lt;= #{taskFlowTimeEnd, jdbcType = TIMESTAMP}
      </if>
      <if test="vehicleReceiveTimeStart != null">
        and t1.vehicle_recive_time &gt;= #{vehicleReceiveTimeStart, jdbcType = TIMESTAMP}
      </if>
      <if test="vehicleReceiveTimeEnd != null">
        and t1.vehicle_recive_time &lt;= #{vehicleReceiveTimeEnd, jdbcType = TIMESTAMP}
      </if>
      <if test="vehicleCheckTimeStart != null">
        and t1.vehicle_check_time &gt;= #{vehicleCheckTimeStart, jdbcType = TIMESTAMP}
      </if>
      <if test="vehicleCheckTimeEnd != null">
        and t1.vehicle_check_time &lt;= #{vehicleCheckTimeEnd, jdbcType = TIMESTAMP}
      </if>
      <!-- 根据有无存储车辆验收时长判断任务是否完成 -->
      <if test="taskFinish != null and taskFinish == 0">
        and t6.duration is null
      </if>
      <if test="taskFinish != null and taskFinish == 1">
        and t6.duration is not null
      </if>
      <if test="reviewToSelFeeFlag != null">
        AND t1.review_to_sel_fee_flag = #{reviewToSelFeeFlag}
      </if>
    </where>
    order by t1.create_time desc
  </select>
</mapper>

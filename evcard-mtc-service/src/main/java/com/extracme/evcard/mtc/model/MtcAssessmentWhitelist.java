package com.extracme.evcard.mtc.model;

import java.util.Date;

public class Mtc<PERSON><PERSON>sment<PERSON><PERSON>elist {
    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column mtc_assessment_whitelist.id
     *
     * @mbggenerated Fri Oct 10 09:43:51 CST 2025
     */
    private Long id;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column mtc_assessment_whitelist.repair_depot_id
     *
     * @mbggenerated Fri Oct 10 09:43:51 CST 2025
     */
    private String repairDepotId;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column mtc_assessment_whitelist.repair_depot_name
     *
     * @mbggenerated Fri Oct 10 09:43:51 CST 2025
     */
    private String repairDepotName;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column mtc_assessment_whitelist.status
     *
     * @mbggenerated Fri Oct 10 09:43:51 CST 2025
     */
    private Integer status;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column mtc_assessment_whitelist.misc_Desc
     *
     * @mbggenerated Fri Oct 10 09:43:51 CST 2025
     */
    private String miscDesc;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column mtc_assessment_whitelist.create_time
     *
     * @mbggenerated Fri Oct 10 09:43:51 CST 2025
     */
    private Date createTime;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column mtc_assessment_whitelist.create_oper_id
     *
     * @mbggenerated Fri Oct 10 09:43:51 CST 2025
     */
    private Long createOperId;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column mtc_assessment_whitelist.create_oper_name
     *
     * @mbggenerated Fri Oct 10 09:43:51 CST 2025
     */
    private String createOperName;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column mtc_assessment_whitelist.update_time
     *
     * @mbggenerated Fri Oct 10 09:43:51 CST 2025
     */
    private Date updateTime;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column mtc_assessment_whitelist.update_oper_id
     *
     * @mbggenerated Fri Oct 10 09:43:51 CST 2025
     */
    private Long updateOperId;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column mtc_assessment_whitelist.update_oper_name
     *
     * @mbggenerated Fri Oct 10 09:43:51 CST 2025
     */
    private String updateOperName;

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column mtc_assessment_whitelist.id
     *
     * @return the value of mtc_assessment_whitelist.id
     *
     * @mbggenerated Fri Oct 10 09:43:51 CST 2025
     */
    public Long getId() {
        return id;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column mtc_assessment_whitelist.id
     *
     * @param id the value for mtc_assessment_whitelist.id
     *
     * @mbggenerated Fri Oct 10 09:43:51 CST 2025
     */
    public void setId(Long id) {
        this.id = id;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column mtc_assessment_whitelist.repair_depot_id
     *
     * @return the value of mtc_assessment_whitelist.repair_depot_id
     *
     * @mbggenerated Fri Oct 10 09:43:51 CST 2025
     */
    public String getRepairDepotId() {
        return repairDepotId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column mtc_assessment_whitelist.repair_depot_id
     *
     * @param repairDepotId the value for mtc_assessment_whitelist.repair_depot_id
     *
     * @mbggenerated Fri Oct 10 09:43:51 CST 2025
     */
    public void setRepairDepotId(String repairDepotId) {
        this.repairDepotId = repairDepotId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column mtc_assessment_whitelist.repair_depot_name
     *
     * @return the value of mtc_assessment_whitelist.repair_depot_name
     *
     * @mbggenerated Fri Oct 10 09:43:51 CST 2025
     */
    public String getRepairDepotName() {
        return repairDepotName;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column mtc_assessment_whitelist.repair_depot_name
     *
     * @param repairDepotName the value for mtc_assessment_whitelist.repair_depot_name
     *
     * @mbggenerated Fri Oct 10 09:43:51 CST 2025
     */
    public void setRepairDepotName(String repairDepotName) {
        this.repairDepotName = repairDepotName;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column mtc_assessment_whitelist.status
     *
     * @return the value of mtc_assessment_whitelist.status
     *
     * @mbggenerated Fri Oct 10 09:43:51 CST 2025
     */
    public Integer getStatus() {
        return status;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column mtc_assessment_whitelist.status
     *
     * @param status the value for mtc_assessment_whitelist.status
     *
     * @mbggenerated Fri Oct 10 09:43:51 CST 2025
     */
    public void setStatus(Integer status) {
        this.status = status;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column mtc_assessment_whitelist.misc_Desc
     *
     * @return the value of mtc_assessment_whitelist.misc_Desc
     *
     * @mbggenerated Fri Oct 10 09:43:51 CST 2025
     */
    public String getMiscDesc() {
        return miscDesc;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column mtc_assessment_whitelist.misc_Desc
     *
     * @param miscDesc the value for mtc_assessment_whitelist.misc_Desc
     *
     * @mbggenerated Fri Oct 10 09:43:51 CST 2025
     */
    public void setMiscDesc(String miscDesc) {
        this.miscDesc = miscDesc;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column mtc_assessment_whitelist.create_time
     *
     * @return the value of mtc_assessment_whitelist.create_time
     *
     * @mbggenerated Fri Oct 10 09:43:51 CST 2025
     */
    public Date getCreateTime() {
        return createTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column mtc_assessment_whitelist.create_time
     *
     * @param createTime the value for mtc_assessment_whitelist.create_time
     *
     * @mbggenerated Fri Oct 10 09:43:51 CST 2025
     */
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column mtc_assessment_whitelist.create_oper_id
     *
     * @return the value of mtc_assessment_whitelist.create_oper_id
     *
     * @mbggenerated Fri Oct 10 09:43:51 CST 2025
     */
    public Long getCreateOperId() {
        return createOperId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column mtc_assessment_whitelist.create_oper_id
     *
     * @param createOperId the value for mtc_assessment_whitelist.create_oper_id
     *
     * @mbggenerated Fri Oct 10 09:43:51 CST 2025
     */
    public void setCreateOperId(Long createOperId) {
        this.createOperId = createOperId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column mtc_assessment_whitelist.create_oper_name
     *
     * @return the value of mtc_assessment_whitelist.create_oper_name
     *
     * @mbggenerated Fri Oct 10 09:43:51 CST 2025
     */
    public String getCreateOperName() {
        return createOperName;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column mtc_assessment_whitelist.create_oper_name
     *
     * @param createOperName the value for mtc_assessment_whitelist.create_oper_name
     *
     * @mbggenerated Fri Oct 10 09:43:51 CST 2025
     */
    public void setCreateOperName(String createOperName) {
        this.createOperName = createOperName;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column mtc_assessment_whitelist.update_time
     *
     * @return the value of mtc_assessment_whitelist.update_time
     *
     * @mbggenerated Fri Oct 10 09:43:51 CST 2025
     */
    public Date getUpdateTime() {
        return updateTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column mtc_assessment_whitelist.update_time
     *
     * @param updateTime the value for mtc_assessment_whitelist.update_time
     *
     * @mbggenerated Fri Oct 10 09:43:51 CST 2025
     */
    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column mtc_assessment_whitelist.update_oper_id
     *
     * @return the value of mtc_assessment_whitelist.update_oper_id
     *
     * @mbggenerated Fri Oct 10 09:43:51 CST 2025
     */
    public Long getUpdateOperId() {
        return updateOperId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column mtc_assessment_whitelist.update_oper_id
     *
     * @param updateOperId the value for mtc_assessment_whitelist.update_oper_id
     *
     * @mbggenerated Fri Oct 10 09:43:51 CST 2025
     */
    public void setUpdateOperId(Long updateOperId) {
        this.updateOperId = updateOperId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column mtc_assessment_whitelist.update_oper_name
     *
     * @return the value of mtc_assessment_whitelist.update_oper_name
     *
     * @mbggenerated Fri Oct 10 09:43:51 CST 2025
     */
    public String getUpdateOperName() {
        return updateOperName;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column mtc_assessment_whitelist.update_oper_name
     *
     * @param updateOperName the value for mtc_assessment_whitelist.update_oper_name
     *
     * @mbggenerated Fri Oct 10 09:43:51 CST 2025
     */
    public void setUpdateOperName(String updateOperName) {
        this.updateOperName = updateOperName;
    }
}
package com.extracme.evcard.mtc.dao;

import com.extracme.evcard.mtc.model.MtcAssessmentWhitelist;
import com.extracme.evcard.mtc.model.MtcAssessmentWhitelistExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface MtcAssessmentWhitelistMapper {
    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table mtc_assessment_whitelist
     *
     * @mbggenerated Fri Oct 10 09:43:51 CST 2025
     */
    int countByExample(MtcAssessmentWhitelistExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table mtc_assessment_whitelist
     *
     * @mbggenerated Fri Oct 10 09:43:51 CST 2025
     */
    int deleteByExample(MtcAssessmentWhitelistExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table mtc_assessment_whitelist
     *
     * @mbggenerated Fri Oct 10 09:43:51 CST 2025
     */
    int deleteByPrimaryKey(Long id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table mtc_assessment_whitelist
     *
     * @mbggenerated Fri Oct 10 09:43:51 CST 2025
     */
    int insert(MtcAssessmentWhitelist record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table mtc_assessment_whitelist
     *
     * @mbggenerated Fri Oct 10 09:43:51 CST 2025
     */
    int insertSelective(MtcAssessmentWhitelist record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table mtc_assessment_whitelist
     *
     * @mbggenerated Fri Oct 10 09:43:51 CST 2025
     */
    List<MtcAssessmentWhitelist> selectByExample(MtcAssessmentWhitelistExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table mtc_assessment_whitelist
     *
     * @mbggenerated Fri Oct 10 09:43:51 CST 2025
     */
    MtcAssessmentWhitelist selectByPrimaryKey(Long id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table mtc_assessment_whitelist
     *
     * @mbggenerated Fri Oct 10 09:43:51 CST 2025
     */
    int updateByExampleSelective(@Param("record") MtcAssessmentWhitelist record, @Param("example") MtcAssessmentWhitelistExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table mtc_assessment_whitelist
     *
     * @mbggenerated Fri Oct 10 09:43:51 CST 2025
     */
    int updateByExample(@Param("record") MtcAssessmentWhitelist record, @Param("example") MtcAssessmentWhitelistExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table mtc_assessment_whitelist
     *
     * @mbggenerated Fri Oct 10 09:43:51 CST 2025
     */
    int updateByPrimaryKeySelective(MtcAssessmentWhitelist record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table mtc_assessment_whitelist
     *
     * @mbggenerated Fri Oct 10 09:43:51 CST 2025
     */
    int updateByPrimaryKey(MtcAssessmentWhitelist record);
}
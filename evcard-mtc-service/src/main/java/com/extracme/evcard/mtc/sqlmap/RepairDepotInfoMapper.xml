<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<!-- RepairDepotInfoMapper，，对应表mtc_repair_depot_info -->
<mapper namespace="com.extracme.evcard.mtc.dao.RepairDepotInfoMapper">
    <!-- 返回结果集Map -->
    <resultMap id="BaseResultMap"
        type="com.extracme.evcard.mtc.model.RepairDepotInfo">
        <id column="id" jdbcType="BIGINT" property="id" />
        <result column="id" jdbcType="BIGINT" property="id" />
        <result column="sso_user_id" jdbcType="BIGINT" property="ssoUserId" />
        <result column="repair_depot_id" jdbcType="VARCHAR"
            property="repairDepotId" />
        <result column="repair_depot_sap_code" jdbcType="VARCHAR" property="repairDepotSapCode" />
        <result column="repair_depot_name" jdbcType="VARCHAR"
            property="repairDepotName" />
        <result column="repair_depot_org_id" jdbcType="VARCHAR"
            property="repairDepotOrgId" />
        <result column="repair_depot_account" jdbcType="VARCHAR"
            property="repairDepotAccount" />
        <result column="repair_depot_grade" jdbcType="VARCHAR"
            property="repairDepotGrade" />
        <result column="maintenance_point" jdbcType="VARCHAR"
            property="maintenancePoint" />
        <result column="province_id" jdbcType="BIGINT" property="provinceId" />
        <result column="city_id" jdbcType="BIGINT" property="cityId" />
        <result column="area_id" jdbcType="BIGINT" property="areaId" />
        <result column="address" jdbcType="VARCHAR" property="address" />
        <result column="linkman_name" jdbcType="VARCHAR"
            property="linkmanName" />
        <result column="cooperation_mode" jdbcType="VARCHAR"
            property="cooperationMode" />
        <result column="accident_contacts" jdbcType="VARCHAR"
            property="accidentContacts" />
        <result column="accident_tel" jdbcType="VARCHAR"
            property="accidentTel" />
        <result column="maintenance_contacts" jdbcType="VARCHAR"
            property="maintenanceContacts" />
        <result column="maintenance_tel" jdbcType="VARCHAR"
            property="maintenanceTel" />
        <result column="repair_depot_longitude" jdbcType="DECIMAL"
            property="repairDepotLongitude" />
        <result column="repair_depot_latitude" jdbcType="DECIMAL"
            property="repairDepotLatitude" />
        <result column="vehicle_model_all_flag" jdbcType="DECIMAL"
            property="vehicleModelAllFlag" />
        <result column="sso_create_time" jdbcType="TIMESTAMP"
            property="ssoCreateTime" />
        <result column="sso_update_time" jdbcType="TIMESTAMP"
            property="ssoUpdateTime" />
        <result column="old_repair_factory_code" jdbcType="VARCHAR"
            property="oldRepairFactoryCode" />
        <result column="status" jdbcType="DECIMAL" property="status" />
        <result column="del_flag" jdbcType="DECIMAL" property="delFlag" />
        <result column="remark" jdbcType="VARCHAR" property="remark" />
        <result column="sso_flag" jdbcType="DECIMAL" property="ssoFlag" />
        <result column="can_repair_item" jdbcType="VARCHAR"
            property="canRepairItem" />
        <result column="warranty_point" jdbcType="VARCHAR"
            property="warrantyPoint" />
        <result column="business_type" jdbcType="BIGINT"
            property="businessType" />
        <result column="business_start_time" jdbcType="BIGINT"
            property="businessStartTime" />
        <result column="business_end_time" jdbcType="BIGINT"
            property="businessEndTime" />
        <result column="create_time" jdbcType="TIMESTAMP"
            property="createTime" />
        <result column="create_oper_id" jdbcType="BIGINT"
            property="createOperId" />
        <result column="create_oper_name" jdbcType="VARCHAR"
            property="createOperName" />
        <result column="update_time" jdbcType="TIMESTAMP"
            property="updateTime" />
        <result column="update_oper_id" jdbcType="BIGINT"
            property="updateOperId" />
        <result column="update_oper_name" jdbcType="VARCHAR"
            property="updateOperName" />
        <result column="repair_depot_type" jdbcType="INTEGER"
                property="repairDepotType" />
        <result column="tax_rate" jdbcType="INTEGER" property="taxRate" />
        <result column="is_show" jdbcType="INTEGER" property="isShow" />
        <result column="is_proxy_operable" jdbcType="INTEGER" property="isProxyOperable" />
    </resultMap>
    <!--数据列 -->
    <sql id="Base_Column_List">
        id,
        sso_user_id,
        repair_depot_id,
        repair_depot_sap_code,
        repair_depot_name,
        repair_depot_org_id,
        repair_depot_account,
        repair_depot_grade,
        repair_depot_type,
        tax_rate,
        is_show,
        maintenance_point,
        province_id,
        city_id,
        area_id,
        address,
        linkman_name,
        cooperation_mode,
        accident_contacts,
        accident_tel,
        maintenance_contacts,
        maintenance_tel,
        repair_depot_longitude,
        repair_depot_latitude,
        vehicle_model_all_flag,
        sso_create_time,
        sso_update_time,
        old_repair_factory_code,
        status,
        del_flag,
        remark,
        sso_flag,
        can_repair_item,
        warranty_point,
        business_type,
        business_start_time,
        business_end_time,
        is_proxy_operable,
        create_time,
        create_oper_id,
        create_oper_name,
        update_time,
        update_oper_id,
        update_oper_name
    </sql>

    <!-- 保存数据 -->
    <insert id="save" parameterType="com.extracme.evcard.mtc.model.RepairDepotInfo"  useGeneratedKeys="true" keyProperty="id">
        insert into
        ${mtcSchema}.mtc_repair_depot_info (
        id,
        sso_user_id,
        repair_depot_id,
        repair_depot_name,
        repair_depot_org_id,
        repair_depot_account,
        repair_depot_grade,
        maintenance_point,
        province_id,
        city_id,
        area_id,
        address,
        linkman_name,
        cooperation_mode,
        accident_contacts,
        accident_tel,
        maintenance_contacts,
        maintenance_tel,
        repair_depot_longitude,
        repair_depot_latitude,
        vehicle_model_all_flag,
        sso_create_time,
        sso_update_time,
        old_repair_factory_code,
        status,
        del_flag,
        remark,
        sso_flag,
        can_repair_item,
        warranty_point,
        business_type,
        business_start_time,
        business_end_time,
        create_time,
        create_oper_id,
        create_oper_name,
        update_time,
        update_oper_id,
        update_oper_name,
        repair_depot_type,
        tax_rate,
        repair_depot_sap_code,
        is_proxy_operable
        ) values (
        #{id,jdbcType=BIGINT},
        #{ssoUserId,jdbcType=BIGINT},
        #{repairDepotId,jdbcType=VARCHAR},
        #{repairDepotName,jdbcType=VARCHAR},
        #{repairDepotOrgId,jdbcType=VARCHAR},
        #{repairDepotAccount,jdbcType=VARCHAR},
        #{repairDepotGrade,jdbcType=VARCHAR},
        #{maintenancePoint,jdbcType=VARCHAR},
        #{provinceId,jdbcType=BIGINT},
        #{cityId,jdbcType=BIGINT},
        #{areaId,jdbcType=BIGINT},
        #{address,jdbcType=VARCHAR},
        #{linkmanName,jdbcType=VARCHAR},
        #{cooperationMode,jdbcType=VARCHAR},
        #{accidentContacts,jdbcType=VARCHAR},
        #{accidentTel,jdbcType=VARCHAR},
        #{maintenanceContacts,jdbcType=VARCHAR},
        #{maintenanceTel,jdbcType=VARCHAR},
        #{repairDepotLongitude,jdbcType=DECIMAL},
        #{repairDepotLatitude,jdbcType=DECIMAL},
        #{vehicleModelAllFlag,jdbcType=DECIMAL},
        #{ssoCreateTime,jdbcType=TIMESTAMP},
        #{ssoUpdateTime,jdbcType=TIMESTAMP},
        #{oldRepairFactoryCode,jdbcType=VARCHAR},
        #{status,jdbcType=DECIMAL},
        #{delFlag,jdbcType=DECIMAL},
        #{remark,jdbcType=VARCHAR},
        #{ssoFlag,jdbcType=DECIMAL},
        #{canRepairItem,jdbcType=VARCHAR},
        #{warrantyPoint,jdbcType=VARCHAR},
        #{businessType,jdbcType=BIGINT},
        #{businessStartTime,jdbcType=BIGINT},
        #{businessEndTime,jdbcType=BIGINT},
        #{createTime,jdbcType=TIMESTAMP},
        #{createOperId,jdbcType=BIGINT},
        #{createOperName,jdbcType=VARCHAR},
        #{updateTime,jdbcType=TIMESTAMP},
        #{updateOperId,jdbcType=BIGINT},
        #{updateOperName,jdbcType=VARCHAR},
        #{repairDepotType,jdbcType=INTEGER},
        #{taxRate,jdbcType=INTEGER},
        #{repairDepotSapCode,jdbcType=VARCHAR},
        #{isProxyOperable,jdbcType=INTEGER}
        )
    </insert>

    <!-- 根据主键取得数据 -->
    <select id="selectById" parameterType="long" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from ${mtcSchema}.mtc_repair_depot_info where id = #{id}
    </select>

    <!--分页获取获取所有数据，分页使用(实际项目中需要自己改造，自己需要几个条件则添加几个条件) -->
    <select id="selectAllPage" resultMap="BaseResultMap"
        parameterType="java.util.Map">
        select
        <include refid="Base_Column_List" />
        from ${mtcSchema}.mtc_repair_depot_info where status = 1
    </select>

    <!-- 根据主键删除数据 -->
    <delete id="deleteById" parameterType="long">
        delete from
        ${mtcSchema}.mtc_repair_depot_info where id =
        #{id,jdbcType=BIGINT}
    </delete>

    <!-- 更新数据 -->
    <update id="updateByIdSelective" parameterType="com.extracme.evcard.mtc.model.RepairDepotInfo">
        update ${mtcSchema}.mtc_repair_depot_info
        <set>
            <if test="entityMap.ssoUserId != null">
                sso_user_id =
                #{entityMap.ssoUserId,jdbcType=BIGINT},
            </if>
            <if test="entityMap.repairDepotId != null">
                repair_depot_id =
                #{entityMap.repairDepotId,jdbcType=VARCHAR},
            </if>
            <if test="entityMap.repairDepotSapCode != null">
                repair_depot_sap_code =
                #{entityMap.repairDepotSapCode,jdbcType=VARCHAR},
            </if>
            <if test="entityMap.repairDepotName != null">
                repair_depot_name =
                #{entityMap.repairDepotName,jdbcType=VARCHAR},
            </if>
            <if test="entityMap.repairDepotOrgId != null">
                repair_depot_org_id =
                #{entityMap.repairDepotOrgId,jdbcType=VARCHAR},
            </if>
            <if test="entityMap.repairDepotAccount != null">
                repair_depot_account =
                #{entityMap.repairDepotAccount,jdbcType=VARCHAR},
            </if>
            <if test="entityMap.repairDepotGrade != null">
                repair_depot_grade =
                #{entityMap.repairDepotGrade,jdbcType=VARCHAR},
            </if>
            <if test="entityMap.maintenancePoint != null">
                maintenance_point =
                #{entityMap.maintenancePoint,jdbcType=VARCHAR},
            </if>
            <if test="entityMap.provinceId != null">
                province_id =
                #{entityMap.provinceId,jdbcType=BIGINT},
            </if>
            <if test="entityMap.cityId != null">
                city_id = #{entityMap.cityId,jdbcType=BIGINT},
            </if>
            <if test="entityMap.areaId != null">
                area_id = #{entityMap.areaId,jdbcType=BIGINT},
            </if>
            <if test="entityMap.address != null">
                address = #{entityMap.address,jdbcType=VARCHAR},
            </if>
            <if test="entityMap.linkmanName != null">
                linkman_name =
                #{entityMap.linkmanName,jdbcType=VARCHAR},
            </if>
            <if test="entityMap.cooperationMode != null">
                cooperation_mode =
                #{entityMap.cooperationMode,jdbcType=VARCHAR},
            </if>
            <if test="entityMap.accidentContacts != null">
                accident_contacts =
                #{entityMap.accidentContacts,jdbcType=VARCHAR},
            </if>
            <if test="entityMap.accidentTel != null">
                accident_tel =
                #{entityMap.accidentTel,jdbcType=VARCHAR},
            </if>
            <if test="entityMap.maintenanceContacts != null">
                maintenance_contacts =
                #{entityMap.maintenanceContacts,jdbcType=VARCHAR},
            </if>
            <if test="entityMap.maintenanceTel != null">
                maintenance_tel =
                #{entityMap.maintenanceTel,jdbcType=VARCHAR},
            </if>
            <if test="entityMap.repairDepotLongitude != null">
                repair_depot_longitude =
                #{entityMap.repairDepotLongitude,jdbcType=DECIMAL},
            </if>
            <if test="entityMap.repairDepotLatitude != null">
                repair_depot_latitude =
                #{entityMap.repairDepotLatitude,jdbcType=DECIMAL},
            </if>
            <if test="entityMap.vehicleModelAllFlag != null">
                vehicle_model_all_flag =
                #{entityMap.vehicleModelAllFlag,jdbcType=DECIMAL},
            </if>
            <if test="entityMap.ssoCreateTime != null">
                sso_create_time =
                #{entityMap.ssoCreateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="entityMap.ssoUpdateTime != null">
                sso_update_time =
                #{entityMap.ssoUpdateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="entityMap.oldRepairFactoryCode != null">
                old_repair_factory_code =
                #{entityMap.oldRepairFactoryCode,jdbcType=VARCHAR},
            </if>
            <if test="entityMap.status != null">
                status = #{entityMap.status,jdbcType=DECIMAL},
            </if>
            <if test="entityMap.delFlag != null">
                del_flag =
                #{entityMap.delFlag,jdbcType=DECIMAL},
            </if>
            <if test="entityMap.remark != null">
                remark = #{entityMap.remark,jdbcType=VARCHAR},
            </if>
            <if test="entityMap.ssoFlag != null">
                sso_flag =
                #{entityMap.ssoFlag,jdbcType=DECIMAL},
            </if>
            <if test="entityMap.canRepairItem != null">
                can_repair_item =
                #{entityMap.canRepairItem,jdbcType=VARCHAR},
            </if>
            <if test="entityMap.warrantyPoint != null">
                warranty_point =
                #{entityMap.warrantyPoint,jdbcType=VARCHAR},
            </if>
            <if test="entityMap.businessType != null">
                business_type =
                #{entityMap.businessType,jdbcType=BIGINT},
            </if>
            <if test="entityMap.businessStartTime != null">
                business_start_time =
                #{entityMap.businessStartTime,jdbcType=BIGINT},
            </if>
            <if test="entityMap.businessEndTime != null">
                business_end_time =
                #{entityMap.businessEndTime,jdbcType=BIGINT},
            </if>
            <if test="entityMap.createOperId != null">
                create_oper_id =
                #{entityMap.createOperId,jdbcType=BIGINT},
            </if>
            <if test="entityMap.createOperName != null">
                create_oper_name =
                #{entityMap.createOperName,jdbcType=VARCHAR},
            </if>
            <if test="entityMap.updateTime != null">
                update_time =
                #{entityMap.updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="entityMap.updateOperId != null">
                update_oper_id =
                #{entityMap.updateOperId,jdbcType=BIGINT},
            </if>
            <if test="entityMap.updateOperName != null">
                update_oper_name =
                #{entityMap.updateOperName,jdbcType=VARCHAR},
            </if>
            <if test="entityMap.taxRate != null">
                tax_rate =
                #{entityMap.taxRate,jdbcType=VARCHAR},
            </if>
            <if test="entityMap.isShow != null">
                is_show =
                #{entityMap.isShow,jdbcType=INTEGER},
            </if>
            <if test="entityMap.isProxyOperable != null">
                is_proxy_operable = #{entityMap.isProxyOperable,jdbcType=INTEGER}
            </if>
        </set>
        where id = #{id,jdbcType=BIGINT}
    </update>

    <!-- 批量插入 -->
    <insert id="batchInsert" parameterType="java.util.List">
        INSERT INTO ${mtcSchema}.mtc_repair_depot_info (

        id,
        sso_user_id,
        repair_depot_id,
        repair_depot_name,
        repair_depot_org_id,
        repair_depot_account,
        repair_depot_grade,
        repair_depot_type,
        tax_rate,
        maintenance_point,
        province_id,
        city_id,
        area_id,
        address,
        linkman_name,
        cooperation_mode,
        accident_contacts,
        accident_tel,
        maintenance_contacts,
        maintenance_tel,
        repair_depot_longitude,
        repair_depot_latitude,
        vehicle_model_all_flag,
        sso_create_time,
        sso_update_time,
        old_repair_factory_code,
        status,
        del_flag,
        remark,
        sso_flag,
        can_repair_item,
        warranty_point,
        business_type,
        business_start_time,
        business_end_time,
        create_time,
        create_oper_id,
        create_oper_name,
        update_time,
        update_oper_id,
        update_oper_name
        )
        <foreach collection="list" item="item" index="index"
            separator="UNION ALL">
            SELECT
            #{item.id,jdbcType=BIGINT},
            #{item.ssoUserId,jdbcType=BIGINT},
            #{item.repairDepotId,jdbcType=VARCHAR},
            #{item.repairDepotName,jdbcType=VARCHAR},
            #{item.repairDepotOrgId,jdbcType=VARCHAR},
            #{item.repairDepotAccount,jdbcType=VARCHAR},
            #{item.repairDepotGrade,jdbcType=VARCHAR},
            #{item.repairDepotType,jdbcType=INTEGER},
            #{item.taxRate,jdbcType=VARCHAR},
            #{item.maintenancePoint,jdbcType=VARCHAR},
            #{item.provinceId,jdbcType=BIGINT},
            #{item.cityId,jdbcType=BIGINT},
            #{item.areaId,jdbcType=BIGINT},
            #{item.address,jdbcType=VARCHAR},
            #{item.linkmanName,jdbcType=VARCHAR},
            #{item.cooperationMode,jdbcType=VARCHAR},
            #{item.accidentContacts,jdbcType=VARCHAR},
            #{item.accidentTel,jdbcType=VARCHAR},
            #{item.maintenanceContacts,jdbcType=VARCHAR},
            #{item.maintenanceTel,jdbcType=VARCHAR},
            #{item.repairDepotLongitude,jdbcType=DECIMAL},
            #{item.repairDepotLatitude,jdbcType=DECIMAL},
            #{item.vehicleModelAllFlag,jdbcType=DECIMAL},
            #{item.ssoCreateTime,jdbcType=TIMESTAMP},
            #{item.ssoUpdateTime,jdbcType=TIMESTAMP},
            #{item.oldRepairFactoryCode,jdbcType=VARCHAR},
            #{item.status,jdbcType=DECIMAL},
            #{item.delFlag,jdbcType=DECIMAL},
            #{item.remark,jdbcType=VARCHAR},
            #{item.ssoFlag,jdbcType=DECIMAL},
            #{item.canRepairItem,jdbcType=VARCHAR},
            #{item.warrantyPoint,jdbcType=VARCHAR},
            #{item.businessType,jdbcType=BIGINT},
            #{item.businessStartTime,jdbcType=BIGINT},
            #{item.businessEndTime,jdbcType=BIGINT},
            #{item.createTime,jdbcType=TIMESTAMP},
            #{item.createOperId,jdbcType=BIGINT},
            #{item.createOperName,jdbcType=VARCHAR},
            #{item.updateTime,jdbcType=TIMESTAMP},
            #{item.updateOperId,jdbcType=BIGINT},
            #{item.updateOperName,jdbcType=VARCHAR}

            FROM DUAL
        </foreach>
    </insert>

    <!-- 单条逻辑删除 -->
    <update id="logicalSelectById">
        update ${mtcSchema}.mtc_repair_depot_info set
        status = 1 , update_time = #{updateTime,jdbcType=TIMESTAMP} ,
        update_oper_id = #{updateOperId,jdbcType=BIGINT} ,
        update_oper_name = #{updateOperName,jdbcType=VARCHAR} where id =
        #{id,jdbcType=BIGINT} and status = 1
    </update>

    <!-- 批量逻辑删除 -->
    <update id="batchLogicalSelectById" parameterType="java.util.List">
        update ${mtcSchema}.mtc_repair_depot_info set status = 1 ,
        update_time = #{updateTime,jdbcType=TIMESTAMP} , update_oper_id
        = #{updateOperId,jdbcType=BIGINT} , update_oper_name =
        #{updateOperName,jdbcType=VARCHAR} where id in
        <foreach collection="list" item="item" index="index"
            open="(" close=")" separator=",">
            ${item}
        </foreach>
        and status = 1
    </update>
    <select id="queryVehicleModel" resultType="com.extracme.evcard.mtc.bo.VehicleModelBO">
        SELECT
        VEHICLE_MODEL_SEQ AS vehicleModelSeq,
        VEHICLE_MODEL_INFO AS
        vehicleModelName
        FROM
        ${isvSchema}.vehicle_model
        where status=1
    </select>

    <select id="getRepairDepotDropDownList"
        resultType="com.extracme.evcard.mtc.bo.RepairDepotDropDownListBO">
        SELECT
        c.repair_depot_id repairDepotId,
        d.repair_depot_name
        repairDepotName
        FROM
        (
        SELECT
        a.repair_depot_id
        FROM
        ${mtcSchema}.mtc_repair_depot_cooperative_branch a
        WHERE
        a.org_id
        LIKE CONCAT(#{orgId}, '%')
        UNION
        SELECT
        b.repair_depot_id
        FROM
        ${mtcSchema}.mtc_repair_depot_info b
        WHERE
        b.repair_depot_org_id
        LIKE CONCAT(#{orgId}, '%')
        ) AS c
        LEFT JOIN
        ${mtcSchema}.mtc_repair_depot_info d ON
        c.repair_depot_id =
        d.repair_depot_id
        WHERE d.sso_flag=1 and d.del_flag=0
        <if test="repairFactoryName != null and repairFactoryName != ''">
            and d.repair_depot_name like
            CONCAT('%',#{repairFactoryName},'%')
        </if>
        ORDER BY
        c.repair_depot_id

    </select>

    <select id="getRepairDepotIds" parameterType="string"
        resultType="string">
        SELECT
        c.repair_depot_id
        FROM
        (
        SELECT
        a.repair_depot_id
        FROM
        ${mtcSchema}.mtc_repair_depot_cooperative_branch a
        WHERE
        a.org_id LIKE CONCAT(#{orgId}, '%')
        UNION
        SELECT
        b.repair_depot_id
        FROM
        ${mtcSchema}.mtc_repair_depot_info b
        WHERE
        b.repair_depot_org_id LIKE CONCAT(#{orgId}, '%')
        ) AS c
        LEFT JOIN
        ${mtcSchema}.mtc_repair_depot_info d ON
        c.repair_depot_id =
        d.repair_depot_id
        WHERE d.sso_flag=1
        ORDER BY
        c.repair_depot_id
    </select>

    <update id="updateStopRepairDepotInfo" parameterType="long">
        UPDATE
        ${mtcSchema}.mtc_repair_depot_info set `status`=0 WHERE id
        =#{id}
    </update>
    <update id="updateStartRepairDepotInfo" parameterType="long">
        UPDATE
        ${mtcSchema}.mtc_repair_depot_info set `status`=1 WHERE id
        =#{id}
    </update>
    <update id="updateEnableProxyOperable" parameterType="long">
        UPDATE ${mtcSchema}.mtc_repair_depot_info
        SET is_proxy_operable = 1
        WHERE id = #{id}
    </update>
    <update id="updateDisableProxyOperable" parameterType="long">
        UPDATE ${mtcSchema}.mtc_repair_depot_info
        SET is_proxy_operable = 0
        WHERE id = #{id}
    </update>

    <update id="updateDeleteRepairDepotInfo" parameterType="long">
        UPDATE ${mtcSchema}.mtc_repair_depot_info set `del_flag`=1 WHERE
        id =#{id}
    </update>

    <!-- 查看维修厂 是否存在 -->
    <select id="selectRepairDepotInfo" resultType="string">
        SELECT
        GROUP_CONCAT(repair_depot_name) AS repairDepotId
        FROM
        mtc.mtc_repair_depot_info
    </select>

    <!-- 和单点登录信息新增 -->
    <insert id="addRepairDepot" parameterType="com.extracme.evcard.mtc.bo.OutRepairDepotBO">
        insert into
        ${mtcSchema}.mtc_repair_depot_info (
        sso_user_id,
        repair_depot_id,
        repair_depot_name,
        repair_depot_account,
        repair_depot_org_id,
        sso_create_time,
        sso_update_time,
        `status`,
        old_repair_factory_code,
        linkman_name
        ) values (
        #{ssoUserId,jdbcType=BIGINT},
        #{repairDepotId,jdbcType=VARCHAR},
        #{repairDepotName,jdbcType=VARCHAR},
        #{repairDepotAccount,jdbcType=VARCHAR},
        #{repairDepotOrgId,jdbcType=VARCHAR},
        #{ssoCreateTime,jdbcType=TIMESTAMP},
        #{ssoUpdateTime,jdbcType=TIMESTAMP},
        0,
        #{oldRepairFactoryCode,jdbcType=VARCHAR},
        #{linkmanName,jdbcType=VARCHAR}
        )
    </insert>

    <!-- 删除变化的修理厂 -->
    <update id="updateDeleteByDepotId" parameterType="java.lang.String">
        UPDATE
        ${mtcSchema}.mtc_repair_depot_info set `del_flag`=1 WHERE
        repair_depot_name =#{repairDepotName} and sso_flag = 1
    </update>

    <!-- 一览查询 -->
    <select id="queryRepairDepot" parameterType="java.util.List"
        resultType="com.extracme.evcard.mtc.bo.RepairDepotDetailBO">
        SELECT
        a.*,
        CASE a.maintenance_point
        WHEN 0 THEN
        N'否'
        ELSE
        N'是'
        END as
        maintenancePoint,
        b.otherOrgId,b.otherOrgName,
        b.vehicleModel,
        h.vehicleWarranty

        FROM

        (
        SELECT
        a.id,
        b.ORG_NAME as
        repairDepotOrgName,
        a.repair_depot_org_id as mainOrgId,
        a.repair_depot_name as repairDepotName,
        a.repair_depot_sap_code as repairDepotSapCode,
        a.repair_depot_account as
        repairDepotAccount,
        a.repair_depot_grade as repairDepotGrade,
        a.repair_depot_longitude AS repairDepotLongitude,
        a.repair_depot_latitude AS repairDepotLatitude,
        a.maintenance_point,
        a.accident_contacts as accidentContacts,
        a.accident_tel as accidentTel,
        a.tax_rate as taxRate,
        a.maintenance_contacts as maintenanceContacts,
        a.maintenance_tel as maintenanceTel,
        case when a.business_type = 0 then '全天'
        when a.business_type = 1 then concat(FROM_UNIXTIME(a.business_start_time*30-30, '%i:%S'),'至',FROM_UNIXTIME(a.business_end_time*30-30, '%i:%S')) end businessTypeName,
        CASE a.cooperation_mode
        WHEN '1' THEN N'长租'
        WHEN '2' THEN N'分时'
        WHEN '3' THEN N'短租'
        WHEN '1,2' THEN N'长租,分时'
        WHEN '1,3' THEN N'长租,短租'
        WHEN '2,3' THEN N'分时,短租'
        WHEN '1,2,3'
        THEN N'长租,分时,短租'
        END as cooperationMode,
        a.can_repair_item as
        canRepairItem,
        CASE a.can_repair_item
        WHEN '0' THEN N'全部'
        WHEN '1'
        THEN N'外观'
        WHEN '2' THEN N'易损件'
        END as
        canRepairItemName,
        a.cooperation_mode as cooperationMode1,
        concat(
        IFNULL(t.PROVINCE,''),
        IFNULL(t1.CITY, ''),
        IFNULL(t2.AREA ,''),
        a.address
        ) as address,
        a.linkman_name as
        linkmanName,
        a.`status`,
        CASE a.`status`
        WHEN 0 THEN
        N'禁用'
        ELSE
        N'启用'
        END statusName,
        a.repair_depot_id as repairDepotId,
        a.repair_depot_type as repairDepotType,
        a.is_show as isShow,
        a.is_proxy_operable as isProxyOperable
        FROM mtc.mtc_repair_depot_info a
        LEFT JOIN isv.org_info b ON a.repair_depot_org_id = b.ORG_ID
        LEFT JOIN siac.province t on t.PROVINCEID=a.province_id
        LEFT JOIN siac.city t1 on t1.CITYID=a.city_id
        LEFT JOIN siac.area t2 on t2.AREAID=a.area_id
        WHERE a.del_flag=0
        AND a.sso_flag = 1
        ORDER BY a.sso_create_time
        DESC
        ) AS a
        LEFT JOIN (
        SELECT
        a.repair_depot_id ,
        GROUP_CONCAT(DISTINCT c.ORG_NAME) as
        otherOrgName,
        GROUP_CONCAT(DISTINCT b.org_id) as otherOrgId,
        GROUP_CONCAT(
        DISTINCT e.VEHICLE_MODEL_INFO
        ) as vehicleModel
        FROM
        mtc.mtc_repair_depot_info a
        LEFT JOIN
        mtc.mtc_repair_depot_cooperative_branch b ON a.repair_depot_id =
        b.repair_depot_id
        LEFT JOIN isv.org_info c ON b.org_id = c.ORG_ID
        LEFT JOIN mtc.mtc_repair_depot_vehicle_model_info d ON
        a.repair_depot_id =
        d.repair_depot_id
        LEFT JOIN isv.vehicle_model
        e ON d.vehicle_model_seq = e.VEHICLE_MODEL_SEQ
        GROUP BY
        a.id
        ) AS b
        ON a.repairDepotId = b.repair_depot_id
        LEFT JOIN (
        SELECT
        a.repair_depot_id,
        GROUP_CONCAT(DISTINCT c.ORG_NAME) AS
        otherOrgName,
        GROUP_CONCAT(DISTINCT b.org_id) AS otherOrgId,
        GROUP_CONCAT(
        DISTINCT e.VEHICLE_MODEL_INFO
        ) AS vehicleWarranty
        FROM
        mtc.mtc_repair_depot_info a
        LEFT JOIN
        mtc.mtc_repair_depot_cooperative_branch b ON a.repair_depot_id =
        b.repair_depot_id
        LEFT JOIN isv.org_info c ON b.org_id = c.ORG_ID
        LEFT JOIN mtc.mtc_repair_depot_vehicle_warranty_info h ON
        a.repair_depot_id = h.repair_depot_id
        LEFT JOIN isv.vehicle_model
        e ON h.vehicle_model_seq = e.VEHICLE_MODEL_SEQ
        GROUP BY
        a.id
        ) AS h
        ON a.repairDepotId = h.repair_depot_id

        WHERE 1=1
        <if test="mainOrgId!=null and mainOrgId!='' and mainOrgId!='00'">
            and a.mainOrgId= #{mainOrgId,jdbcType=VARCHAR}
        </if>
        <if test="otherOrgId!=null and otherOrgId!=''">
            and b.otherOrgId like
            concat('%',#{otherOrgId,jdbcType=VARCHAR},'%')
        </if>
        <if test="repairDepotGrade!=null and repairDepotGrade!=''">
            and a.repairDepotGrade =
            #{repairDepotGrade,jdbcType=VARCHAR}
        </if>
        <if test="maintenancePoint!=null and maintenancePoint!=''">
            and a.maintenance_point =
            #{maintenancePoint,jdbcType=VARCHAR}
        </if>
        <if test="repairDepotName!=null and repairDepotName!=''">
            and a.repairDepotName like
            concat('%',#{repairDepotName,jdbcType=VARCHAR},'%')
        </if>
        <if test="status1!=null">
            AND a.`status`=#{status1,jdbcType=DECIMAL}
        </if>
        <if test="repairDepotType != null">
            AND a.`repairDepotType`=#{repairDepotType, jdbcType=INTEGER}
        </if>
        <if test="isShow != null">
            AND a.`isShow`=#{isShow, jdbcType=INTEGER}
        </if>

    </select>

    <select id="queryRepairDepotForWt" parameterType="java.util.List"
            resultType="com.extracme.evcard.mtc.bo.RepairDepotDetailBO">
        SELECT
        a.*,
        CASE a.maintenance_point
        WHEN 0 THEN
        N'否'
        ELSE
        N'是'
        END as
        maintenancePoint,
        b.otherOrgId,b.otherOrgName,
        b.vehicleModel,
        h.vehicleWarranty

        FROM

        (
        SELECT
        a.id,
        b.ORG_NAME as
        repairDepotOrgName,
        a.repair_depot_org_id as mainOrgId,
        a.repair_depot_name as repairDepotName,
        a.repair_depot_sap_code as repairDepotSapCode,
        a.repair_depot_account as
        repairDepotAccount,
        a.repair_depot_grade as repairDepotGrade,
        a.repair_depot_longitude AS repairDepotLongitude,
        a.repair_depot_latitude AS repairDepotLatitude,
        a.maintenance_point,
        a.accident_contacts as accidentContacts,
        a.accident_tel as accidentTel,
        a.maintenance_contacts as maintenanceContacts,
        a.maintenance_tel as maintenanceTel,
        case when a.business_type = 0 then '全天'
        when a.business_type = 1 then concat(FROM_UNIXTIME(a.business_start_time*30-30, '%i:%S'),'至',FROM_UNIXTIME(a.business_end_time*30-30, '%i:%S')) end businessTypeName,
        CASE a.cooperation_mode
        WHEN '1' THEN N'长租'
        WHEN '2' THEN N'分时'
        WHEN '3' THEN N'短租'
        WHEN '1,2' THEN N'长租,分时'
        WHEN '1,3' THEN N'长租,短租'
        WHEN '2,3' THEN N'分时,短租'
        WHEN '1,2,3'
        THEN N'长租,分时,短租'
        END as cooperationMode,
        a.can_repair_item as
        canRepairItem,
        CASE a.can_repair_item
        WHEN '0' THEN N'全部'
        WHEN '1'
        THEN N'外观'
        WHEN '2' THEN N'易损件'
        END as
        canRepairItemName,
        a.cooperation_mode as cooperationMode1,
        concat(
        IFNULL(t.PROVINCE,''),
        IFNULL(t1.CITY, ''),
        IFNULL(t2.AREA ,''),
        a.address
        ) as address,
        a.linkman_name as
        linkmanName,
        a.`status`,
        CASE a.`status`
        WHEN 0 THEN
        N'禁用'
        ELSE
        N'启用'
        END statusName,
        a.repair_depot_id as repairDepotId,
        a.repair_depot_type as repairDepotType,
        a.is_show as isShow,
        a.province_id as provinceId

        FROM
        mtc.mtc_repair_depot_info
        a
        LEFT JOIN isv.org_info b ON
        a.repair_depot_org_id = b.ORG_ID
        left
        JOIN siac.province t on
        t.PROVINCEID=a.province_id
        left JOIN
        siac.city t1 on
        t1.CITYID=a.city_id
        left JOIN siac.area t2 on
        t2.AREAID=a.area_id
        WHERE a.del_flag=0 AND a.sso_flag = 1 ORDER
        BY a.sso_create_time
        DESC
        ) AS a
        LEFT JOIN (
        SELECT
        a.repair_depot_id ,
        GROUP_CONCAT(DISTINCT c.ORG_NAME) as
        otherOrgName,
        GROUP_CONCAT(DISTINCT b.org_id) as otherOrgId,
        GROUP_CONCAT(
        DISTINCT e.VEHICLE_MODEL_INFO
        ) as vehicleModel
        FROM
        mtc.mtc_repair_depot_info a
        LEFT JOIN
        mtc.mtc_repair_depot_cooperative_branch b ON a.repair_depot_id =
        b.repair_depot_id
        LEFT JOIN isv.org_info c ON b.org_id = c.ORG_ID
        LEFT JOIN mtc.mtc_repair_depot_vehicle_model_info d ON
        a.repair_depot_id =
        d.repair_depot_id
        LEFT JOIN isv.vehicle_model
        e ON d.vehicle_model_seq = e.VEHICLE_MODEL_SEQ
        GROUP BY
        a.id
        ) AS b
        ON a.repairDepotId = b.repair_depot_id
        LEFT JOIN (
        SELECT
        a.repair_depot_id,
        GROUP_CONCAT(DISTINCT c.ORG_NAME) AS
        otherOrgName,
        GROUP_CONCAT(DISTINCT b.org_id) AS otherOrgId,
        GROUP_CONCAT(
        DISTINCT e.VEHICLE_MODEL_INFO
        ) AS vehicleWarranty
        FROM
        mtc.mtc_repair_depot_info a
        LEFT JOIN
        mtc.mtc_repair_depot_cooperative_branch b ON a.repair_depot_id =
        b.repair_depot_id
        LEFT JOIN isv.org_info c ON b.org_id = c.ORG_ID
        LEFT JOIN mtc.mtc_repair_depot_vehicle_warranty_info h ON
        a.repair_depot_id = h.repair_depot_id
        LEFT JOIN isv.vehicle_model
        e ON h.vehicle_model_seq = e.VEHICLE_MODEL_SEQ
        GROUP BY
        a.id
        ) AS h
        ON a.repairDepotId = h.repair_depot_id

        WHERE 1=1
        <if test="mainOrgId!=null and mainOrgId!='' and mainOrgId!='00'">
            and (a.mainOrgId= #{mainOrgId,jdbcType=VARCHAR}
            or b.otherOrgId like
            concat('%',#{mainOrgId,jdbcType=VARCHAR},'%'))
        </if>
        <if test="repairDepotGradeList != null and repairDepotGradeList.size()>0">
            and a.repairDepotGrade in
            <foreach collection="repairDepotGradeList" item="repairDepotGrade"
                     separator="," open="(" close=")">
                #{repairDepotGrade}
            </foreach>
        </if>
        <if test="repairDepotName!=null and repairDepotName!=''">
            and a.repairDepotName like
            concat('%',#{repairDepotName,jdbcType=VARCHAR},'%')
        </if>
        <if test="status1!=null">
            AND a.`status`=#{status1,jdbcType=DECIMAL}
        </if>
        <if test="repairDepotType!=null">
            AND a.`repairDepotType`=#{repairDepotType,jdbcType=INTEGER}
        </if>
        <if test="isShow!=null">
            AND a.`isShow`=#{isShow,jdbcType=INTEGER}
        </if>


    </select>

    <!-- 条件查询 -->
    <select id="queryRepairDepotByInfo" parameterType="com.extracme.evcard.mtc.bo.QueryRepairDepotBo"
            resultType="com.extracme.evcard.mtc.dto.RepairDepotInfoDTO">
        select
        <include refid="Base_Column_List" />
        from ${mtcSchema}.mtc_repair_depot_info
        where 1 = 1
        <if test="repairDepotGradeList != null and repairDepotGradeList.size()>0">
            and repair_depot_grade in
            <foreach collection="repairDepotGradeList" item="repairDepotGrade"
                     separator="," open="(" close=")">
                #{repairDepotGrade}
            </foreach>
        </if>
        <if test="repairDepotName != null and repairDepotName != ''">
            and repair_depot_name like
            CONCAT('%',#{repairDepotName},'%')
        </if>
        <if test="orgId != null and orgId != ''">
            and (
                repair_depot_org_id = #{orgId} or
                repair_depot_id in (select repair_depot_id from ${mtcSchema}.mtc_repair_depot_cooperative_branch where org_id = #{orgId})
            )
        </if>
        <if test="isAll != null and orgId != '' and isAll == 2">
            and status = 1
        </if>
    </select>


    <select id="getShowRepairDepot" resultType="com.extracme.evcard.mtc.model.RepairDepotInfo">
        select
        <include refid="Base_Column_List" />
        from ${mtcSchema}.mtc_repair_depot_info
        where repair_depot_type = 1 and isShow = 1 and status = 0
    </select>

    <select id="queryOrgId" parameterType="java.util.List"
        resultType="java.lang.Integer">
        select count(*) from isv.org_info WHERE
        ORG_ID=#{orgId}
    </select>
    <select id="queryVehicleModelSeq" parameterType="java.util.List" resultType="java.lang.Integer">
        SELECT count(*)
        from isv.vehicle_model
        WHERE VEHICLE_MODEL_SEQ=#{seq}
    </select>

    <select id="queryDispatchRepairDepotList"
        parameterType="com.extracme.evcard.mtc.bo.DispatchRepairDepotListQueryBO"
        resultType="com.extracme.evcard.mtc.bo.DispatchRepairDepotListBO">
        SELECT
        d.repair_depot_id as repairDepotId,
        d.repair_depot_type as repairDepotType,
        d.repair_depot_name as
        repairDepotName,
        d.repair_depot_grade as repairGrade,
        d.repair_depot_longitude as repairDepotLongitude,
        d.repair_depot_latitude as repairDepotLatitude,
        d.old_repair_factory_code as oldRepairFactoryCode,
        d.address,
        d.can_repair_item as canRepairItem,
        d.province_id as provinceId,
        d.city_id as cityId,
        d.area_id as areaId,
        p.PROVINCE as provinceName,
        citys.CITY as cityName,
        areas.AREA as areaName,
        d.vehicle_model_all_flag as
        vehicleModelAllFlag,
        IFNULL(g.count,0) AS repairingVehicleCnt
        FROM
        (
        SELECT
        a.repair_depot_id
        FROM
        ${mtcSchema}.mtc_repair_depot_cooperative_branch a
        WHERE
        a.org_id
        = #{orgId}
        UNION
        SELECT
        b.repair_depot_id
        FROM
        ${mtcSchema}.mtc_repair_depot_info b
        WHERE
        b.status = 1 and
        b.repair_depot_org_id =
        #{orgId}
        ) AS c
        LEFT JOIN ${mtcSchema}.mtc_repair_depot_info d ON
        c.repair_depot_id = d.repair_depot_id and d.status = 1
        LEFT JOIN siac.province p ON p.PROVINCEID=d.province_id
        LEFT JOIN siac.city citys ON citys.CITYID=d.city_id
        LEFT JOIN siac.area areas ON areas.AREAID=d.area_id
        LEFT JOIN (
        SELECT
        SUM(a.num) as count,
        h.repair_depot_id
        FROM
        (
        SELECT
        COUNT(*) as num,
        repair_depot_id,
        repair_type_id
        FROM
        ${mtcSchema}.mtc_repair_task
        WHERE
        repair_type_id IN (1, 2, 6, 9, 10)
        AND vehicle_transfer_task_schedule
        != 100
        AND vehicle_check_task_schedule != 610
        AND
        check_result_flag != 0
        AND reassignment_task_schedule != 410
        AND
        vehicle_recive_time IS NOT NULL
        and org_id = #{orgId}
        GROUP BY
        repair_depot_id
        UNION ALL
        SELECT
        COUNT(*) as num,
        repair_depot_id,
        repair_type_id
        FROM
        ${mtcSchema}.mtc_repair_task
        WHERE
        repair_type_id = 3
        AND vehicle_transfer_task_schedule != 100
        AND
        insurance_quote_task_schedule != 230
        AND
        reassignment_task_schedule != 410
        AND vehicle_recive_time IS NOT
        NULL
        and org_id = #{orgId}
        GROUP BY repair_depot_id
        ) AS a
        LEFT JOIN
        mtc_repair_depot_info h
        ON h.repair_depot_id = a.repair_depot_id and h.status = 1
        GROUP BY a.repair_depot_id) g ON g.repair_depot_id =
        c.repair_depot_id
        WHERE d.status = 1 and d.sso_flag = 1 and
        d.del_flag=0
        <if test="repairGrade == 'A'.toString()">
            AND d.repair_depot_grade = 'A'
        </if>
        <if test="repairGrade == 'B'.toString()">
            AND (d.repair_depot_grade = 'A' or
            d.repair_depot_grade = 'B')
        </if>
        <if test="repairGrade == 'C'.toString()">
            AND (d.repair_depot_grade = 'A' or
            d.repair_depot_grade = 'B' or
            d.repair_depot_grade = 'C')
        </if>
        ORDER BY
        c.repair_depot_id
    </select>
    <select id="queryDepotInfo" parameterType="java.lang.String" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM
        mtc_repair_depot_info
        WHERE
        repair_depot_id = #{repairDepotId}
    </select>
    <select id="queryDepotOrgId" parameterType="java.util.List"
        resultType="string">
        SELECT
        repair_depot_org_id AS repairDepotOrgId
        FROM
        mtc_repair_depot_info
        WHERE
        repair_depot_id = #{repairDepotId}
    </select>
    <select id="queryDepotSapCode" resultType="java.lang.String">
        SELECT
        repair_depot_sap_code AS repairDepotSapCode
        FROM
        mtc_repair_depot_info
        WHERE
        repair_depot_id = #{repairDepotId}
    </select>

    <select id="queryRepairDepotDetail" parameterType="java.util.List" resultType="com.extracme.evcard.mtc.bo.RepairDepotInfoBO">
        SELECT
        a.id as id,
        a.repair_depot_id as repairDepotId,
        a.repair_depot_sap_code as repairDepotSapCode,
        a.repair_depot_name as
        repairDepotName,
        a.repair_depot_account as
        repairDepotAccount,
        a.repair_depot_grade as repairDepotGrade,
        a.maintenance_point as
        maintenancePoint,
        a.can_repair_item as
        canRepairItem,
        a.repair_depot_type as repairDepotType,
        a.tax_rate as taxRate,
        case when a.can_repair_item=0 then '全部'
        when a.can_repair_item=1 then '外观'
        when a.can_repair_item=2 then '易损件' end canRepairItemName,
        a.business_type as businessType,
        case when a.business_type = 0 then '全天'
        when a.business_type = 1 then concat(FROM_UNIXTIME(a.business_start_time*30-30, '%i:%S'),'至',FROM_UNIXTIME(a.business_end_time*30-30, '%i:%S')) end businessTypeName,
        a.business_start_time as businessStartTime,
        a.business_end_time
        as businessEndTime,
        a.warranty_point as warrantyPoint,
        case when a.warranty_point = 0 then '否'
        when a.warranty_point = 1 then '是' end warrantyPointName,
        b.ORG_NAME
        as mainOrgName,
        b.ORG_ID
        as mainOrgId,
        GROUP_CONCAT(DISTINCT
        c.org_id) as otherOrgId,
        a.province_id as provinceId,
        a.city_id as
        cityId,
        a.area_id as
        areaId,
        a.address as address,
        GROUP_CONCAT(DISTINCT
        d.vehicle_model_seq) as vehicleModel,
        GROUP_CONCAT( DISTINCT
        e.vehicle_model_seq) AS vehicleWarranty,
        a.linkman_name as
        linkmanName,
        a.`status` as status,
        a.cooperation_mode as cooperationMode,
        case when
        a.cooperation_mode = '1,2,3' then
        '长租,分时,短租' when
        a.cooperation_mode = '1,2' then '长租,分时' when
        a.cooperation_mode
        = '2,3' then '分时,短租' when a.cooperation_mode =
        '1' then '长租'
        when a.cooperation_mode = '2' then '分时' when
        a.cooperation_mode
        = '2' then '短租' when a.cooperation_mode =
        '1,3' then '长租,短租'
        WHEN a.cooperation_mode = '' THEN '空'
        end as cooperationModeName,
        a.accident_contacts as accidentContacts,
        a.accident_tel as accidentTel,
        a.maintenance_contacts as maintenanceContacts,
        a.maintenance_tel as maintenanceTel ,
        a.repair_depot_longitude as repairDepotLongitude,
        a.repair_depot_latitude as repairDepotLatitude,
        a.vehicle_model_all_flag as vehicleModelAllFlag,
        CONCAT(d.PROVINCE,e.CITY,f.AREA,a.address) as addressDetail,
        a.is_proxy_operable as isProxyOperable
        FROM mtc.mtc_repair_depot_info a
        LEFT JOIN mtc.mtc_repair_depot_vehicle_model_info d on d.repair_depot_id=a.repair_depot_id
        LEFT JOIN mtc.mtc_repair_depot_vehicle_warranty_info e ON e.repair_depot_id = a.repair_depot_id
        LEFT JOIN mtc.mtc_repair_depot_cooperative_branch c on c.repair_depot_id=a.repair_depot_id
        LEFT JOIN isv.org_info b ON a.repair_depot_org_id= b.ORG_ID
        left join ${siacSchema}.province d on a.province_id = d.PROVINCEID
        left join ${siacSchema}.city e on a.city_id = e.CITYID
        left join ${siacSchema}.area f on f.AREAID = a.area_id
        WHERE
            a.id=#{id} GROUP BY id
    </select>

    <select id="queryRepairId" parameterType="long" resultType="string">
        SELECT
        repair_depot_id
        FROM
        mtc.mtc_repair_depot_info
        WHERE
        id =
        #{id}
    </select>

    <!-- 和单点系统修改 -->
    <update id="updateRepairDepot" parameterType="com.extracme.evcard.mtc.bo.OutRepairDepotBO">
        UPDATE mtc.mtc_repair_depot_info
        <set>
            <if test="repairDepotId != null and repairDepotId != ''">
                repair_depot_id=#{repairDepotId},
            </if>
            <if test="repairDepotName != null and repairDepotName != ''">
                repair_depot_name = #{repairDepotName},
            </if>
            <if test="linkmanName != null and linkmanName != ''">
                linkman_name = #{linkmanName},
            </if>
            <if test="repairDepotOrgId != null and repairDepotOrgId != ''">
                repair_depot_org_id =#{repairDepotOrgId},
            </if>
            <if
                test="repairDepotAccount != null and repairDepotAccount != ''">
                repair_depot_account =#{repairDepotAccount},
            </if>
            <if test="ssoCreateTime != null">
                sso_create_time =#{ssoCreateTime},
            </if>
            <if test="ssoUpdateTime != null">
                sso_update_time =#{ssoUpdateTime},
            </if>
            <if test="oldRepairFactoryCode != null">
                old_repair_factory_code =
                #{oldRepairFactoryCode},
            </if>
            sso_flag = 1
        </set>
        WHERE
        sso_user_id = #{ssoUserId}
    </update>
    <!-- 车辆驳回 -->
    <update id="updateStatusByTaskNo" parameterType="string">
        UPDATE
        mtc.mtc_repair_task
        SET `status` = 0
        WHERE
        task_no =#{taskNo}
    </update>
    <select id="queryDepotStatus" parameterType="java.util.List"
        resultType="string">
        SELECT `status` from mtc.mtc_repair_depot_info WHERE
        repair_depot_id=#{repairDepotId}
    </select>

    <!-- 取得修理厂省市区 -->
    <select id="getDepotArea" resultType="com.extracme.evcard.mtc.bo.AtcPricingAdoptBO">
        SELECT
        TRIM(b.PROVINCE)
        as provinceName,
        TRIM(c.CITY) as cityName,
        TRIM(d.AREA) as
        areaName
        FROM
        ${mtcSchema}.mtc_repair_depot_info a
        left join
        ${siacSchema}.province b on a.province_id = b.PROVINCEID
        left
        join ${siacSchema}.city c on a.city_id = c.CITYID
        left join
        ${siacSchema}.area d on d.AREAID = a.area_id
        WHERE
        repair_depot_id=#{repairDepotId}
    </select>

    <!-- 取得车型seq对应的车型名称 -->
    <select id="getVehicleModelInfo" resultType="string">
        SELECT
        VEHICLE_MODEL_INFO FROM ${isvSchema}.vehicle_model
        where
        VEHICLE_MODEL_SEQ = #{vehicleModelSeq};
    </select>

    <!-- 取得车型seq对应的车型名称 -->
    <select id="getVehicleModelSeq" resultType="long">
        SELECT
            VEHICLE_MODEL_SEQ FROM ${isvSchema}.vehicle_model
        where
            VEHICLE_MODEL_INFO = #{vehicleModelInfo};
    </select>

    <!-- 根据修理厂名称获取修理厂ID -->
    <select id="selectByName" parameterType="java.lang.String"
        resultType="com.extracme.evcard.mtc.bo.RepairDepotDetailBO">
        select
        repair_depot_id as repairDepotId
        from
        ${mtcSchema}.mtc_repair_depot_info
        where
        repair_depot_name =
        #{repairDepotName} and sso_flag = 1
    </select>

    <!-- 根据登录ID获取修理厂ID -->
    <select id="selectBySSO" parameterType="java.lang.Long"
        resultType="com.extracme.evcard.mtc.bo.RepairDepotDetailBO">
        select
        repair_depot_id as repairDepotId
        from
        ${mtcSchema}.mtc_repair_depot_info
        where
        del_flag = 0 and
        sso_user_id =
        #{ssoUserId,jdbcType=BIGINT}
    </select>

    <select id="selectRepairDepotInfoList" resultType="com.extracme.evcard.mtc.bo.repairDepotNameListBO">
        SELECT
        repair_depot_name AS repairDepotName,
        sso_user_id as ssoUserId,
        linkman_name as linkmanName
        FROM
        mtc.mtc_repair_depot_info
    </select>

    <!-- 开始营业 -->
    <update id="updateBusinessStartTime" parameterType="Integer"
        useGeneratedKeys="true" keyProperty="id">
        update
        ${mtcSchema}.mtc_repair_depot_info
        SET `status` = 1,
        update_oper_name='system',
        update_oper_id=-1,
        update_time
        =CURRENT_TIMESTAMP(3)
        WHERE
        business_start_time=#{businessStartTime,jdbcType=BIGINT}
        and
        del_flag=0
        and business_type=1
    </update>

    <!-- 停止营业 -->
    <update id="updateBusinessEndTime" parameterType="Integer"
        useGeneratedKeys="true" keyProperty="id">
        update
        ${mtcSchema}.mtc_repair_depot_info
        SET `status` = 0,
        update_oper_name='system',
        update_oper_id=-1,
        update_time
        =CURRENT_TIMESTAMP(3)
        WHERE
        business_end_time=#{businessEndTime,jdbcType=BIGINT}
        and
        del_flag=0
        and business_type=1
    </update>

    <!-- 查询修理厂项目总数 -->
    <select id="getAllRepairDepotNum" parameterType="Map"
        resultType="Integer">
        SELECT
        count(*)
        FROM
        ${mtcSchema}.mtc_repair_depot_info a
        LEFT JOIN
        ${isvSchema}.org_info b ON a.repair_depot_id = b.ORG_ID
        <if test="orgId!=null and orgId!=''">
            and a.repair_depot_org_id like
            concat(#{orgId,jdbcType=VARCHAR},'%')
        </if>
        <if test="repairDepotName!=null and repairDepotName!=''">
            and a.repair_depot_name =
            #{repairDepotName,jdbcType=VARCHAR}
        </if>
        <if test="maintenancePoint!=null and maintenancePoint!=''">
            and
            a.maintenance_point=#{maintenancePoint,jdbcType=VARCHAR}
        </if>
        <if test="repairDepotGrade!=null and repairDepotGrade!=''">
            and
            a.repair_depot_grade=#{repairDepotGrade,jdbcType=VARCHAR}
        </if>
        <if test="status!=null">
            and a.`status` = #{status,jdbcType=DECIMAL}
        </if>
        <if test="isShow!=null">
            and a.`is_show` = #{isShow,jdbcType=INTEGER}
        </if>

    </select>



    <select id="getRepairDepotDropDownListAga"
        resultType="com.extracme.evcard.mtc.bo.RepairDepotDropDownListBO">
        SELECT
        c.repair_depot_id repairDepotId,
        d.repair_depot_name
        repairDepotName,
        repair_depot_grade repairDepotGrade
        FROM
        (
        SELECT
        a.repair_depot_id
        FROM
        ${mtcSchema}.mtc_repair_depot_cooperative_branch a
        WHERE
        a.org_id
        LIKE CONCAT(#{orgId}, '%')
        UNION
        SELECT
        b.repair_depot_id
        FROM
        ${mtcSchema}.mtc_repair_depot_info b
        WHERE
        b.status = 1 and
        b.repair_depot_org_id
        LIKE CONCAT(#{orgId}, '%')
        ) AS c
        LEFT JOIN
        ${mtcSchema}.mtc_repair_depot_info d ON
        c.repair_depot_id =
        d.repair_depot_id
        WHERE d.sso_flag=1 and d.status = 1
        <if test="repiarDepotIds != null and repiarDepotIds.size()>0">
            and c.repair_depot_id not in
            <foreach collection="repiarDepotIds" item="repiarDepotId"
                separator="," open="(" close=")">
                #{repiarDepotId}
            </foreach>
        </if>
        <if test="repairFactoryName != null and repairFactoryName != ''">
            and d.repair_depot_name like
            CONCAT('%',#{repairFactoryName},'%')
        </if>
        ORDER BY
        c.repair_depot_id

    </select>

    <select id="getRepairType" resultType="String">
        select repair_depot_grade from ${mtcSchema}.mtc_repair_depot_info where repair_depot_id = #{repairDepotId}
    </select>

    <select id="selectByDepotName" parameterType="string" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from
        ${mtcSchema}.mtc_repair_depot_info
        where
        repair_depot_name = #{repairDepotName,jdbcType=VARCHAR} and del_flag = 0
    </select>

    <select id="selectValidDepotList" parameterType="long" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from
        ${mtcSchema}.mtc_repair_depot_info
        where
        del_flag = 0
    </select>

    <select id="selectTaskByDepot" parameterType="list" resultType="string">
        select
        distinct
        repair_depot_id
        from
        ${mtcSchema}.mtc_repair_task
        where
        repair_depot_id in
        <foreach collection="list" item="item" open="(" close=")" separator=",">
            #{item,jdbcType=VARCHAR}
        </foreach>
    </select>

    <update id="batchUpdateDelFlag" parameterType="list">
        update
        ${mtcSchema}.mtc_repair_depot_info
        set
        del_flag = 1,
        update_time = sysdate(),
        update_oper_id = -1,
        update_oper_name = 'synRepairDepotList'
        where
        repair_depot_id in
        <foreach collection="list" item="item" open="(" close=")" separator=",">
            #{item,jdbcType=VARCHAR}
        </foreach>
    </update>


    <update id="updateIsShow" parameterType="com.extracme.evcard.mtc.model.RepairDepotInfo">
        UPDATE mtc.mtc_repair_depot_info
        <set>
            <if test="isShow != null">
                is_show=#{isShow},
            </if>
        </set>
        WHERE
        repair_depot_id = #{repairDepotId}
    </update>
</mapper>

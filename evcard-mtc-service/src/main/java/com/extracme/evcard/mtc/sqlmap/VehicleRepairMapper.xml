<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<!-- RepairTaskMapper，，对应表repair_task -->
<mapper namespace="com.extracme.evcard.mtc.dao.VehicleRepairMapper">

    <!--数据列-->
    <sql id="VehicleRepair_Column_List" >
            id,
            task_no,
            org_id,
            org_name,
            vehicle_no,
            vehicle_model_info,
            vin,
            repair_type_id,
            repair_type_name,
            repair_depot_org_id,
            repair_depot_name,
            task_inflow_time,
            vehicle_recive_time,
            vehicle_repair_task_schedule,
            check_unqualified_reason,
            resurvey_flag,
            update_time,
            insurance_company_name

    </sql>
  
  <resultMap id="VehicleRepairResultMap" type="com.extracme.evcard.mtc.bo.VehicleRepairResultBO">
    <id column="id" jdbcType="BIGINT" property="id"/>
    <result column="id" jdbcType="BIGINT" property="id"/>
    <result column="task_no" jdbcType="VARCHAR" property="taskNo"/>
    <result column="org_id" jdbcType="VARCHAR" property="orgId"/>
    <result column="org_name" jdbcType="VARCHAR" property="orgName"/>
    <result column="vehicle_no" jdbcType="VARCHAR" property="vehicleNo"/>
    <result column="vehicle_model_info" jdbcType="VARCHAR" property="vehicleModelInfo"/>
    <result column="vin" jdbcType="VARCHAR" property="vin"/>
    <result column="repair_type_id" jdbcType="DECIMAL" property="repairTypeId"/>
    <result column="repair_type_name" jdbcType="VARCHAR" property="repairTypeName"/>
    <result column="repair_depot_org_id" jdbcType="VARCHAR" property="repairDepotOrgId"/>
    <result column="repair_depot_name" jdbcType="VARCHAR" property="repairDepotName"/>
    <result column="task_inflow_time" jdbcType="TIMESTAMP" property="taskInflowTime"/>
    <result column="vehicle_recive_time" jdbcType="TIMESTAMP" property="vehicleReciveTime"/>
    <result column="vehicle_repair_task_schedule" jdbcType="BIGINT" property="vehicleRepairTaskSchedule"/>
    <result column="resurvey_flag" jdbcType="BIGINT" property="resurveyFlag"/>
    <result column="check_unqualified_reason" jdbcType="VARCHAR" property="checkUnqualifiedReason"/>
    <result column="isOutTime" jdbcType="VARCHAR" property="isOutTime"/>
    <result column="insurance_company_name" jdbcType="VARCHAR" property="insuranceCompanyName"/>
    <result column="repair_type_name" jdbcType="VARCHAR" property="repairTypeName"/>
    <result column="repair_grade" jdbcType="VARCHAR" property="repairGrade"/>
    <result column="renttype" jdbcType="INTEGER" property="renttype"/>
    <result column="fact_operate_tag" jdbcType="INTEGER" property="factOperateTag"/>
    <result column="review_to_sel_fee_flag" jdbcType="INTEGER" property="reviewToSelFeeFlag"/>
  </resultMap>
  
  <select id="selectVehicleRepair" resultMap="VehicleRepairResultMap" parameterType="com.extracme.evcard.mtc.bo.VehicleRepairBO">
    select
    id,
    task_no,
    org_id,
    org_name,
    vehicle_no,
    repair_grade,
    vehicle_model_info,
    vin,
    repair_type_id,
    case when maintain_to_repair_flag =1 then N'自费维修（原车俩保养）' else repair_type_name end as repair_type_name,
    repair_depot_org_id,
    repair_depot_name,
    task_inflow_time,
    vehicle_recive_time,
    vehicle_repair_task_schedule,
    check_unqualified_reason,
    resurvey_flag,
    update_time,
    insurance_company_name,
    case when date_add(verification_loss_check_time, interval expected_repair_days day) > (case when vehicle_repair_time
    is not null then vehicle_repair_time else now() end)
    then 1 else 0 end as isOutTime,
    renttype,
    fact_operate_tag,
    review_to_sel_fee_flag,
    repair_depot_type
    from ${mtcSchema}.mtc_repair_task
    <where>
      status = 1
      and vehicle_repair_task_schedule != '-1'
      <if test="orgId != null and orgId != ''">
          and org_id like concat(#{orgId,jdbcType=VARCHAR},'%')
      </if>
      <if test="repairDepotId != null and repairDepotId != '' and repairDepotId !=5">
        and repair_depot_id = #{repairDepotId,jdbcType=VARCHAR}
      </if>
     <if test="repairDepotId == null or repairDepotId == ''">
        <!-- 仅在 500/510 状态下要求 is_proxy_operable = 1，其它状态不受限制 -->
        <!-- 车辆维修环节 -->
        and (
          (vehicle_repair_task_schedule in (500, 510) and is_proxy_operable = 1)
          or (vehicle_repair_task_schedule not in (500, 510))
        )
     </if>
      <if test="vehicleNo != null and vehicleNo != ''">
        and vehicle_no = #{vehicleNo,jdbcType=VARCHAR}
      </if>
      <if test="taskNo != null and taskNo != ''">
        and task_no = #{taskNo,jdbcType=VARCHAR}
      </if>
      <if test="repairDepotName != null and repairDepotName != ''">
        and repair_depot_name like concat('%',#{repairDepotName,jdbcType=VARCHAR},'%')
      </if>
      <if test="vin != null and vin != ''">
        and vin = #{vin,jdbcType=VARCHAR}
      </if>
      <if test="resurveyFlag != null and resurveyFlag != ''">
        and resurvey_flag = #{resurveyFlag,jdbcType=VARCHAR}
      </if>
      <if test="repairTypeId != null and repairTypeId != '' and repairTypeId != 5">
        and repair_type_id = #{repairTypeId,jdbcType=VARCHAR}
      </if>
      <if test="repairTypeId == 5 ">
        and maintain_to_repair_flag = 1 and repair_type_id != 3
      </if>
      <if test="taskInflowTimeStart != null and taskInflowTimeStart != ''">
        and task_inflow_time >= #{taskInflowTimeStart,jdbcType=VARCHAR}
      </if>
      <if test="taskInflowTimeEnd != null and taskInflowTimeEnd != ''">
        and DATE_ADD(task_inflow_time,INTERVAL -1 day) <![CDATA[<]]> #{taskInflowTimeEnd,jdbcType=VARCHAR}
      </if>
      <if test="vehicleReciveTimeStart != null and vehicleReciveTimeStart != ''">
        and vehicle_recive_time >= #{vehicleReciveTimeStart,jdbcType=VARCHAR}
      </if>
      <if test="vehicleReciveTimeEnd != null and vehicleReciveTimeEnd != ''">
        and DATE_ADD(vehicle_recive_time,INTERVAL -1 day) <![CDATA[<]]> #{vehicleReciveTimeEnd,jdbcType=VARCHAR}
      </if>
      <if test="vehicleModelSeq != null and vehicleModelSeq != ''">
        and vehicle_model_seq = #{vehicleModelSeq,jdbcType=VARCHAR}
      </if>
      <if test="vehicleRepairTaskSchedule != null and vehicleRepairTaskSchedule != ''">
        and vehicle_repair_task_schedule = #{vehicleRepairTaskSchedule,jdbcType=VARCHAR}
      </if>
      <if test="isOutTime != null and isOutTime != ''">
        and
        case when date_add(vehicle_recive_time, interval expected_repair_days day) > (case when vehicle_repair_time is
        not null then vehicle_repair_time else now() end)
        then 1 else 0 end = #{isOutTime,jdbcType=VARCHAR}
      </if>
      <if test="renttype != null">
        and renttype = #{renttype}
      </if>
      <if test="factOperateTag != null">
        and fact_operate_tag = #{factOperateTag}
      </if>
      <if test="renttypeList != null">
        <if test="renttypeList.size() > 0">
          and renttype in
          <foreach collection="renttypeList" item="item" open="(" close=")" separator=",">
            #{item}
          </foreach>
        </if>
        <if test="renttypeList.size() == 0">
          and renttype = -2
        </if>
      </if>
      <if test="accidentNo != null and accidentNo != ''">
        and accident_no = #{accidentNo}
      </if>
      <if test="reviewToSelFeeFlag != null">
        and review_to_sel_fee_flag = #{reviewToSelFeeFlag}
      </if>
    </where>
    order by create_time DESC
  </select>

     <select id="selectVehicleRepairInfo" resultMap="VehicleRepairResultMap" parameterType="com.extracme.evcard.mtc.bo.VehicleRepairBO" >
        select
            id,
            task_no,
            org_id,
            org_name,
            vehicle_no,
            repair_grade,
            vehicle_model_info,
            vin,
            repair_type_id,
            case when maintain_to_repair_flag =1 then N'自费维修（原车俩保养）' else repair_type_name end as repair_type_name,
            repair_depot_org_id,
            repair_depot_name,
            task_inflow_time,
            vehicle_recive_time,
            vehicle_repair_task_schedule,
            check_unqualified_reason,
            resurvey_flag,
            update_time,
            insurance_company_name,
        case when date_add(vehicle_recive_time, interval expected_repair_days day) > (case when vehicle_repair_time is not null then vehicle_repair_time else now() end)
        then 1 else 0 end as isOutTime
        from ${mtcSchema}.mtc_repair_task
        <where>
            status = 1
            and vehicle_repair_task_schedule != '-1'
            and vehicle_repair_task_schedule != '520'
            <if test="orgId != null and orgId != ''">
            and org_id = #{orgId,jdbcType=VARCHAR}
            </if>
            <if test="repairDepotId != null and repairDepotId != '' and repairDepotId !=5">
            and repair_depot_id = #{repairDepotId,jdbcType=VARCHAR}
            </if>
            <if test="repairDepotId == null or repairDepotId == ''">
              <!-- 仅在 500/510 状态下要求 is_proxy_operable = 1，其它状态不受限制 -->
              <!-- 车辆维修环节 -->
              and (
                (vehicle_repair_task_schedule in (500, 510) and is_proxy_operable = 1)
                or (vehicle_repair_task_schedule not in (500, 510))
              )
            </if>
            <if test="repairDepotId == 5">
            and maintain_to_repair_flag = 1
            </if>
            <if test="vehicleNo != null and vehicleNo != ''">
            and vehicle_no like concat(#{vehicleNo},'%')
            </if>
            <if test="taskNo != null and taskNo != ''">
            and task_no = #{taskNo,jdbcType=VARCHAR}
            </if>
            <if test="repairDepotName != null and repairDepotName != ''">
            and repair_depot_name like concat('%',#{repairDepotName,jdbcType=VARCHAR},'%')
            </if>

            <if test="vin != null and vin != ''">
            and vin = #{vin,jdbcType=VARCHAR}
            </if>
            <if test="resurveyFlag != null and resurveyFlag != ''">
            and resurvey_flag = #{resurveyFlag,jdbcType=VARCHAR}
            </if>
            <if test="repairTypeId != null and repairTypeId != '' and repairTypeId != 5">
            and repair_type_id = #{repairTypeId,jdbcType=VARCHAR}
            </if>
            <if test= "repairTypeId == 5 " >
            and maintain_to_repair_flag = 1 and repair_type_id != 3
            </if>
            <if test="taskInflowTimeStart != null and taskInflowTimeStart != ''">
            and task_inflow_time >= #{taskInflowTimeStart,jdbcType=VARCHAR}
            </if>
            <if test="taskInflowTimeEnd != null and taskInflowTimeEnd != ''">
            and DATE_ADD(task_inflow_time,INTERVAL -1 day) <![CDATA[<]]> #{taskInflowTimeEnd,jdbcType=VARCHAR}
            </if>
            <if test="vehicleReciveTimeStart != null and vehicleReciveTimeStart != ''">
            and vehicle_recive_time >= #{vehicleReciveTimeStart,jdbcType=VARCHAR}
            </if>
            <if test="vehicleReciveTimeEnd != null and vehicleReciveTimeEnd != ''">
            and DATE_ADD(vehicle_recive_time,INTERVAL -1 day) <![CDATA[<]]> #{vehicleReciveTimeEnd,jdbcType=VARCHAR}
            </if>
            <if test="vehicleModelSeq != null and vehicleModelSeq != ''">
            and vehicle_model_seq = #{vehicleModelSeq,jdbcType=VARCHAR}
            </if>
            <if test="vehicleRepairTaskSchedule != null and vehicleRepairTaskSchedule != ''">
            and vehicle_repair_task_schedule = #{vehicleRepairTaskSchedule,jdbcType=VARCHAR}
            </if>
            <if test="isOutTime != null and isOutTime != ''">
                and
                 case when date_add(vehicle_recive_time, interval expected_repair_days day) > (case when vehicle_repair_time is not null then vehicle_repair_time else now() end)
                 then 1 else 0 end  = #{isOutTime,jdbcType=VARCHAR}
            </if>
        </where>
        order by update_time DESC
     </select>
  <select id="selectCountVehicleRepair" resultType="com.extracme.evcard.mtc.bo.InsuranceQuoteCountResultBO"
          parameterType="com.extracme.evcard.mtc.bo.VehicleRepairBO">
    select
    vehicle_repair_task_schedule as countKey,
    count(1) as countValue
    from ${mtcSchema}.mtc_repair_task
    <where>
      status = 1
      and vehicle_repair_task_schedule != '-1'
      <if test="orgId != null and orgId != ''">
          and org_id like concat(#{orgId,jdbcType=VARCHAR},'%')
      </if>
      <if test="repairDepotId != null and repairDepotId != ''">
        and repair_depot_id = #{repairDepotId,jdbcType=VARCHAR}
      </if>
      <if test="repairDepotId == null or repairDepotId == ''">
        <!-- 仅在 500/510 状态下要求 is_proxy_operable = 1，其它状态不受限制 -->
        <!-- 车辆维修环节 -->
        and (
          (vehicle_repair_task_schedule in (500, 510) and is_proxy_operable = 1)
          or (vehicle_repair_task_schedule not in (500, 510))
        )
      </if>
      <if test="vehicleNo != null and vehicleNo != ''">
        and vehicle_no = #{vehicleNo,jdbcType=VARCHAR}
      </if>
      <if test="taskNo != null and taskNo != ''">
        and task_no = #{taskNo,jdbcType=VARCHAR}
      </if>
      <if test="repairDepotName != null and repairDepotName != ''">
        and repair_depot_name like concat('%',#{repairDepotName,jdbcType=VARCHAR},'%')
      </if>
      <if test="vin != null and vin != ''">
        and vin = #{vin,jdbcType=VARCHAR}
      </if>
      <if test="resurveyFlag != null and resurveyFlag != ''">
        and resurvey_flag = #{resurveyFlag,jdbcType=VARCHAR}
      </if>
      <if test="repairTypeId != null and repairTypeId != '' and repairTypeId != 5">
        and repair_type_id = #{repairTypeId,jdbcType=VARCHAR}
      </if>
      <if test="repairTypeId == 5 ">
        and maintain_to_repair_flag = 1 and repair_type_id != 3
      </if>
      <if test="taskInflowTimeStart != null and taskInflowTimeStart != ''">
        and task_inflow_time >= #{taskInflowTimeStart,jdbcType=VARCHAR}
      </if>
      <if test="taskInflowTimeEnd != null and taskInflowTimeEnd != ''">
        and DATE_ADD(task_inflow_time,INTERVAL -1 day) <![CDATA[<]]> #{taskInflowTimeEnd,jdbcType=VARCHAR}
      </if>
      <if test="vehicleReciveTimeStart != null and vehicleReciveTimeStart != ''">
        and vehicle_recive_time >= #{vehicleReciveTimeStart,jdbcType=VARCHAR}
      </if>
      <if test="vehicleReciveTimeEnd != null and vehicleReciveTimeEnd != ''">
        and DATE_ADD(vehicle_recive_time,INTERVAL -1 day) <![CDATA[<]]> #{vehicleReciveTimeEnd,jdbcType=VARCHAR}
      </if>
      <if test="vehicleModelSeq != null and vehicleModelSeq != ''">
        and vehicle_model_seq = #{vehicleModelSeq,jdbcType=VARCHAR}
      </if>
      <if test="isOutTime != null and isOutTime != ''">
        and case when date_add(vehicle_recive_time, interval expected_repair_days day) > (case when vehicle_repair_time
        is not null then vehicle_repair_time else now() end)
        then 1 else 0 end = #{isOutTime,jdbcType=VARCHAR}
      </if>
      <if test="renttype != null">
        and renttype = #{renttype}
      </if>
      <if test="factOperateTag != null">
        and fact_operate_tag = #{factOperateTag}
      </if>
      <if test="renttypeList != null">
        <if test="renttypeList.size() > 0">
          and renttype in
          <foreach collection="renttypeList" item="item" open="(" close=")" separator=",">
            #{item}
          </foreach>
        </if>
        <if test="renttypeList.size() == 0">
          and renttype = -2
        </if>
      </if>
      <if test="accidentNo != null and accidentNo != ''">
        and accident_no = #{accidentNo}
      </if>
      <if test="reviewToSelFeeFlag != null">
        and review_to_sel_fee_flag = #{reviewToSelFeeFlag}
      </if>
    </where>
    group by vehicle_repair_task_schedule
  </select>

     <select id="selectCountVehicleRepairInfo" resultType="com.extracme.evcard.mtc.bo.InsuranceQuoteCountResultBO" parameterType="com.extracme.evcard.mtc.bo.VehicleRepairBO" >
        select
        vehicle_repair_task_schedule as countKey,
        count(1) as countValue
        from ${mtcSchema}.mtc_repair_task
        <where>
            status = 1
            and vehicle_repair_task_schedule != '-1'
            and vehicle_repair_task_schedule !='520'
            <if test="orgId != null and orgId != ''">
            and org_id = #{orgId,jdbcType=VARCHAR}
            </if>
            <if test="repairDepotId != null and repairDepotId != ''">
            and repair_depot_id = #{repairDepotId,jdbcType=VARCHAR}
            </if>
            <if test="repairDepotId ==null or repairDepotId == ''">
              <!-- 仅在 500/510 状态下要求 is_proxy_operable = 1，其它状态不受限制 -->
              <!-- 车辆维修环节 -->
              and (
                (vehicle_repair_task_schedule in (500, 510) and is_proxy_operable = 1)
                or (vehicle_repair_task_schedule not in (500, 510))
              )
            </if>
            <if test="vehicleNo != null and vehicleNo != ''">
            and vehicle_no like concat(#{vehicleNo},'%')
            </if>
            <if test="taskNo != null and taskNo != ''">
            and task_no = #{taskNo,jdbcType=VARCHAR}
            </if>
            <if test="repairDepotName != null and repairDepotName != ''">
            and repair_depot_name like concat('%',#{repairDepotName,jdbcType=VARCHAR},'%')
            </if>
            <if test="vin != null and vin != ''">
            and vin = #{vin,jdbcType=VARCHAR}
            </if>
            <if test="resurveyFlag != null and resurveyFlag != ''">
            and resurvey_flag = #{resurveyFlag,jdbcType=VARCHAR}
            </if>
            <if test="repairTypeId != null and repairTypeId != '' and repairTypeId != 5">
            and repair_type_id = #{repairTypeId,jdbcType=VARCHAR}
            </if>
            <if test= "repairTypeId == 5 " >
            and maintain_to_repair_flag = 1 and repair_type_id != 3
            </if>
            <if test="taskInflowTimeStart != null and taskInflowTimeStart != ''">
            and task_inflow_time >= #{taskInflowTimeStart,jdbcType=VARCHAR}
            </if>
            <if test="taskInflowTimeEnd != null and taskInflowTimeEnd != ''">
            and DATE_ADD(task_inflow_time,INTERVAL -1 day) <![CDATA[<]]> #{taskInflowTimeEnd,jdbcType=VARCHAR}
            </if>
            <if test="vehicleReciveTimeStart != null and vehicleReciveTimeStart != ''">
            and vehicle_recive_time >= #{vehicleReciveTimeStart,jdbcType=VARCHAR}
            </if>
            <if test="vehicleReciveTimeEnd != null and vehicleReciveTimeEnd != ''">
            and DATE_ADD(vehicle_recive_time,INTERVAL -1 day) <![CDATA[<]]> #{vehicleReciveTimeEnd,jdbcType=VARCHAR}
            </if>
            <if test="vehicleModelSeq != null and vehicleModelSeq != ''">
            and vehicle_model_seq = #{vehicleModelSeq,jdbcType=VARCHAR}
            </if>
            <if test="isOutTime != null and isOutTime != ''">
                and case when date_add(vehicle_recive_time, interval expected_repair_days day) > (case when vehicle_repair_time is not null then vehicle_repair_time else now() end)
                 then 1 else 0 end = #{isOutTime,jdbcType=VARCHAR}
            </if>
        </where>
        group by vehicle_repair_task_schedule
     </select>
    <!-- 车辆维修修改状态-->
    <update id="updateVehicleRepair" parameterType="com.extracme.evcard.mtc.bo.InsuranceQuoteUpdateTaskBO">
        update ${mtcSchema}.mtc_repair_task
        <set>
            <if test="vehicleRepairTaskSchedule != null and vehicleRepairTaskSchedule != 0">
                vehicle_repair_task_schedule = #{vehicleRepairTaskSchedule,jdbcType=BIGINT},
            </if>
            <if test="vehicleCheckTaskSchedule != null and vehicleCheckTaskSchedule != 0">
                vehicle_check_task_schedule = #{vehicleCheckTaskSchedule,jdbcType=BIGINT},
            </if>
            <if test="currentTache != null and currentTache != 0">
                current_tache = #{currentTache,jdbcType=BIGINT},
            </if>
            <if test="updateTime != null">
                vehicle_repair_time = #{updateTime,jdbcType=TIMESTAMP} ,
                update_time = #{updateTime,jdbcType=TIMESTAMP} ,
            </if>
            <if test="updateOperId != null and updateOperId != 0">
                update_oper_id = #{updateOperId,jdbcType=BIGINT} ,
            </if>
            <if test="updateOperName != null and updateOperName != ''">
                update_oper_name = #{updateOperName,jdbcType=VARCHAR} ,
            </if>
            <if test="isUsedApplets != null and isUsedApplets != ''">
                is_used_applets = #{isUsedApplets,jdbcType=VARCHAR} ,
            </if>
            <if test="custPaysDirect != null">
                cust_pays_direct = #{custPaysDirect,jdbcType=INTEGER},
            </if>
            <if test="custAmount != null">
                cust_amount = #{custAmount,jdbcType=DECIMAL},
            </if>
            <if test="userAssumedAmount != null">
                user_assumed_amount = #{userAssumedAmount,jdbcType=DECIMAL},
            </if>
            <if test="notUserAssumedAmount != null">
                not_user_assumed_amount = #{notUserAssumedAmount,jdbcType=DECIMAL},
            </if>
            <if test="selfFundedAmount != null">
                self_funded_amount = #{selfFundedAmount,jdbcType=DECIMAL},
            </if>
            over_time_reasons = #{overTimeReasons,jdbcType=VARCHAR},
        </set>
        where id = #{id,jdbcType=VARCHAR} and status = 1
    </update>

     <select id="getOverTime" resultType="string">
     select
     case when b.subtask_no!=''  and a.vehicle_check_task_schedule=610 THEN 1
     when date_add(a.verification_loss_check_time, interval a.expected_repair_days day) > (case when a.vehicle_repair_time is not null then a.vehicle_repair_time else now() end)
     then 1 else 0 end as isOutTime
     from ${mtcSchema}.mtc_repair_task a
     LEFT JOIN mtc_vehicle_advance_check_task b ON a.task_no = b.task_no AND b.`status` =1
     where a.id = #{id,jdbcType=BIGINT}
     </select>

     <select id="queryVehicleRepairRecord" parameterType="com.extracme.evcard.mtc.bo.VehicleRepairRecordQueryBO" resultType="com.extracme.evcard.mtc.bo.VehicleRepairRecordBO">
     select
      distinct a.id as id,
      a.task_no as taskNo,
      a.repair_type_id as repairTypeId,
      if(a.maintain_to_repair_flag = 1 and a.repair_type_id != 3, '自费维修（原车辆保养）', a.repair_type_name) as repairTypeName,
      a.repair_depot_name as repairDepotName,
      a.vehicle_insurance_total_amount as vehicleRepairTotalAmount,
      a.task_inflow_time as taskInflowTime,
      a.vehicle_check_time as vehicleCheckTime,
      a.total_mileage as totalMileage,
      a.terminal_mileage as terminalMileage
     from ${mtcSchema}.mtc_repair_task a
     left join ${mtcSchema}.mtc_loss_fit_info b on (a.task_no = b.task_no and b.status = 1 and b.check_state != '')
     left join ${mtcSchema}.mtc_loss_repair_info c on (a.task_no = c.task_no and c.status = 1 and c.check_state != '')
     left join ${mtcSchema}.mtc_repair_item_check_info d on (a.task_no = d.task_no and d.status = 1 and d.insurance_pre_review_status = 0)
     where a.vin = #{vin, jdbcType=VARCHAR}
     and a.id != #{id, jdbcType=BIGINT}
     and a.repair_type_id in (1, 2, 3, 6, 7, 9, 10)
     <if test="taskNo != null and taskNo != ''">
      and a.task_no = #{taskNo, jdbcType=VARCHAR}
     </if>
     <if test="repairTypeId != null and repairTypeId != '' and repairTypeId != 5">
      and a.repair_type_id = #{repairTypeId,jdbcType=VARCHAR}
     </if>
     <if test= "repairTypeId == 5 " >
      and a.maintain_to_repair_flag = 1 and repair_type_id != 3
     </if>
     <if test="repairDepotName != null and repairDepotName != ''">
      and a.repair_depot_name like concat('%',#{repairDepotName,jdbcType=VARCHAR},'%')
     </if>
     <if test="taskInflowTimeStart != null">
      and a.task_inflow_time >= #{taskInflowTimeStart, jdbcType=TIMESTAMP}
     </if>
     <if test="taskInflowTimeEnd != null">
      and DATE_ADD(a.task_inflow_time,INTERVAL -1 day) <![CDATA[<]]> #{taskInflowTimeEnd,jdbcType=TIMESTAMP}
     </if>
     <if test="vehicleCheckDayStart != null">
      and a.vehicle_check_time >= #{vehicleCheckDayStart, jdbcType=TIMESTAMP}
     </if>
     <if test="vehicleCheckDayEnd != null">
      and DATE_ADD(a.vehicle_check_time,INTERVAL -1 day) <![CDATA[<]]> #{vehicleCheckDayEnd,jdbcType=TIMESTAMP}
     </if>
     <if test="partName != null and partName != ''">
      and b.item_name = #{partName, jdbcType=VARCHAR}
     </if>
     <if test="repairName != null and repairName != ''">
      and (c.item_name = #{repairName, jdbcType=VARCHAR} or d.item_name = #{repairName, jdbcType=VARCHAR})
     </if>
    ORDER BY a.create_time DESC
    </select>
</mapper>

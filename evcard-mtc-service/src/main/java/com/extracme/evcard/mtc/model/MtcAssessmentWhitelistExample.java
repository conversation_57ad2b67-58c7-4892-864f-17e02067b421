package com.extracme.evcard.mtc.model;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class MtcAssessmentWhitelistExample {
    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database table mtc_assessment_whitelist
     *
     * @mbggenerated Fri Oct 10 09:43:51 CST 2025
     */
    protected String orderByClause;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database table mtc_assessment_whitelist
     *
     * @mbggenerated Fri Oct 10 09:43:51 CST 2025
     */
    protected boolean distinct;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database table mtc_assessment_whitelist
     *
     * @mbggenerated Fri Oct 10 09:43:51 CST 2025
     */
    protected List<Criteria> oredCriteria;

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table mtc_assessment_whitelist
     *
     * @mbggenerated Fri Oct 10 09:43:51 CST 2025
     */
    public MtcAssessmentWhitelistExample() {
        oredCriteria = new ArrayList<Criteria>();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table mtc_assessment_whitelist
     *
     * @mbggenerated Fri Oct 10 09:43:51 CST 2025
     */
    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table mtc_assessment_whitelist
     *
     * @mbggenerated Fri Oct 10 09:43:51 CST 2025
     */
    public String getOrderByClause() {
        return orderByClause;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table mtc_assessment_whitelist
     *
     * @mbggenerated Fri Oct 10 09:43:51 CST 2025
     */
    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table mtc_assessment_whitelist
     *
     * @mbggenerated Fri Oct 10 09:43:51 CST 2025
     */
    public boolean isDistinct() {
        return distinct;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table mtc_assessment_whitelist
     *
     * @mbggenerated Fri Oct 10 09:43:51 CST 2025
     */
    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table mtc_assessment_whitelist
     *
     * @mbggenerated Fri Oct 10 09:43:51 CST 2025
     */
    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table mtc_assessment_whitelist
     *
     * @mbggenerated Fri Oct 10 09:43:51 CST 2025
     */
    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table mtc_assessment_whitelist
     *
     * @mbggenerated Fri Oct 10 09:43:51 CST 2025
     */
    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table mtc_assessment_whitelist
     *
     * @mbggenerated Fri Oct 10 09:43:51 CST 2025
     */
    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table mtc_assessment_whitelist
     *
     * @mbggenerated Fri Oct 10 09:43:51 CST 2025
     */
    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    /**
     * This class was generated by MyBatis Generator.
     * This class corresponds to the database table mtc_assessment_whitelist
     *
     * @mbggenerated Fri Oct 10 09:43:51 CST 2025
     */
    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(Long value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(Long value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(Long value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(Long value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThan(Long value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(Long value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<Long> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<Long> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(Long value1, Long value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(Long value1, Long value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andRepairDepotIdIsNull() {
            addCriterion("repair_depot_id is null");
            return (Criteria) this;
        }

        public Criteria andRepairDepotIdIsNotNull() {
            addCriterion("repair_depot_id is not null");
            return (Criteria) this;
        }

        public Criteria andRepairDepotIdEqualTo(String value) {
            addCriterion("repair_depot_id =", value, "repairDepotId");
            return (Criteria) this;
        }

        public Criteria andRepairDepotIdNotEqualTo(String value) {
            addCriterion("repair_depot_id <>", value, "repairDepotId");
            return (Criteria) this;
        }

        public Criteria andRepairDepotIdGreaterThan(String value) {
            addCriterion("repair_depot_id >", value, "repairDepotId");
            return (Criteria) this;
        }

        public Criteria andRepairDepotIdGreaterThanOrEqualTo(String value) {
            addCriterion("repair_depot_id >=", value, "repairDepotId");
            return (Criteria) this;
        }

        public Criteria andRepairDepotIdLessThan(String value) {
            addCriterion("repair_depot_id <", value, "repairDepotId");
            return (Criteria) this;
        }

        public Criteria andRepairDepotIdLessThanOrEqualTo(String value) {
            addCriterion("repair_depot_id <=", value, "repairDepotId");
            return (Criteria) this;
        }

        public Criteria andRepairDepotIdLike(String value) {
            addCriterion("repair_depot_id like", value, "repairDepotId");
            return (Criteria) this;
        }

        public Criteria andRepairDepotIdNotLike(String value) {
            addCriterion("repair_depot_id not like", value, "repairDepotId");
            return (Criteria) this;
        }

        public Criteria andRepairDepotIdIn(List<String> values) {
            addCriterion("repair_depot_id in", values, "repairDepotId");
            return (Criteria) this;
        }

        public Criteria andRepairDepotIdNotIn(List<String> values) {
            addCriterion("repair_depot_id not in", values, "repairDepotId");
            return (Criteria) this;
        }

        public Criteria andRepairDepotIdBetween(String value1, String value2) {
            addCriterion("repair_depot_id between", value1, value2, "repairDepotId");
            return (Criteria) this;
        }

        public Criteria andRepairDepotIdNotBetween(String value1, String value2) {
            addCriterion("repair_depot_id not between", value1, value2, "repairDepotId");
            return (Criteria) this;
        }

        public Criteria andRepairDepotNameIsNull() {
            addCriterion("repair_depot_name is null");
            return (Criteria) this;
        }

        public Criteria andRepairDepotNameIsNotNull() {
            addCriterion("repair_depot_name is not null");
            return (Criteria) this;
        }

        public Criteria andRepairDepotNameEqualTo(String value) {
            addCriterion("repair_depot_name =", value, "repairDepotName");
            return (Criteria) this;
        }

        public Criteria andRepairDepotNameNotEqualTo(String value) {
            addCriterion("repair_depot_name <>", value, "repairDepotName");
            return (Criteria) this;
        }

        public Criteria andRepairDepotNameGreaterThan(String value) {
            addCriterion("repair_depot_name >", value, "repairDepotName");
            return (Criteria) this;
        }

        public Criteria andRepairDepotNameGreaterThanOrEqualTo(String value) {
            addCriterion("repair_depot_name >=", value, "repairDepotName");
            return (Criteria) this;
        }

        public Criteria andRepairDepotNameLessThan(String value) {
            addCriterion("repair_depot_name <", value, "repairDepotName");
            return (Criteria) this;
        }

        public Criteria andRepairDepotNameLessThanOrEqualTo(String value) {
            addCriterion("repair_depot_name <=", value, "repairDepotName");
            return (Criteria) this;
        }

        public Criteria andRepairDepotNameLike(String value) {
            addCriterion("repair_depot_name like", value, "repairDepotName");
            return (Criteria) this;
        }

        public Criteria andRepairDepotNameNotLike(String value) {
            addCriterion("repair_depot_name not like", value, "repairDepotName");
            return (Criteria) this;
        }

        public Criteria andRepairDepotNameIn(List<String> values) {
            addCriterion("repair_depot_name in", values, "repairDepotName");
            return (Criteria) this;
        }

        public Criteria andRepairDepotNameNotIn(List<String> values) {
            addCriterion("repair_depot_name not in", values, "repairDepotName");
            return (Criteria) this;
        }

        public Criteria andRepairDepotNameBetween(String value1, String value2) {
            addCriterion("repair_depot_name between", value1, value2, "repairDepotName");
            return (Criteria) this;
        }

        public Criteria andRepairDepotNameNotBetween(String value1, String value2) {
            addCriterion("repair_depot_name not between", value1, value2, "repairDepotName");
            return (Criteria) this;
        }

        public Criteria andStatusIsNull() {
            addCriterion("status is null");
            return (Criteria) this;
        }

        public Criteria andStatusIsNotNull() {
            addCriterion("status is not null");
            return (Criteria) this;
        }

        public Criteria andStatusEqualTo(Integer value) {
            addCriterion("status =", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusNotEqualTo(Integer value) {
            addCriterion("status <>", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusGreaterThan(Integer value) {
            addCriterion("status >", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusGreaterThanOrEqualTo(Integer value) {
            addCriterion("status >=", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusLessThan(Integer value) {
            addCriterion("status <", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusLessThanOrEqualTo(Integer value) {
            addCriterion("status <=", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusIn(List<Integer> values) {
            addCriterion("status in", values, "status");
            return (Criteria) this;
        }

        public Criteria andStatusNotIn(List<Integer> values) {
            addCriterion("status not in", values, "status");
            return (Criteria) this;
        }

        public Criteria andStatusBetween(Integer value1, Integer value2) {
            addCriterion("status between", value1, value2, "status");
            return (Criteria) this;
        }

        public Criteria andStatusNotBetween(Integer value1, Integer value2) {
            addCriterion("status not between", value1, value2, "status");
            return (Criteria) this;
        }

        public Criteria andMiscDescIsNull() {
            addCriterion("misc_Desc is null");
            return (Criteria) this;
        }

        public Criteria andMiscDescIsNotNull() {
            addCriterion("misc_Desc is not null");
            return (Criteria) this;
        }

        public Criteria andMiscDescEqualTo(String value) {
            addCriterion("misc_Desc =", value, "miscDesc");
            return (Criteria) this;
        }

        public Criteria andMiscDescNotEqualTo(String value) {
            addCriterion("misc_Desc <>", value, "miscDesc");
            return (Criteria) this;
        }

        public Criteria andMiscDescGreaterThan(String value) {
            addCriterion("misc_Desc >", value, "miscDesc");
            return (Criteria) this;
        }

        public Criteria andMiscDescGreaterThanOrEqualTo(String value) {
            addCriterion("misc_Desc >=", value, "miscDesc");
            return (Criteria) this;
        }

        public Criteria andMiscDescLessThan(String value) {
            addCriterion("misc_Desc <", value, "miscDesc");
            return (Criteria) this;
        }

        public Criteria andMiscDescLessThanOrEqualTo(String value) {
            addCriterion("misc_Desc <=", value, "miscDesc");
            return (Criteria) this;
        }

        public Criteria andMiscDescLike(String value) {
            addCriterion("misc_Desc like", value, "miscDesc");
            return (Criteria) this;
        }

        public Criteria andMiscDescNotLike(String value) {
            addCriterion("misc_Desc not like", value, "miscDesc");
            return (Criteria) this;
        }

        public Criteria andMiscDescIn(List<String> values) {
            addCriterion("misc_Desc in", values, "miscDesc");
            return (Criteria) this;
        }

        public Criteria andMiscDescNotIn(List<String> values) {
            addCriterion("misc_Desc not in", values, "miscDesc");
            return (Criteria) this;
        }

        public Criteria andMiscDescBetween(String value1, String value2) {
            addCriterion("misc_Desc between", value1, value2, "miscDesc");
            return (Criteria) this;
        }

        public Criteria andMiscDescNotBetween(String value1, String value2) {
            addCriterion("misc_Desc not between", value1, value2, "miscDesc");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIsNull() {
            addCriterion("create_time is null");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIsNotNull() {
            addCriterion("create_time is not null");
            return (Criteria) this;
        }

        public Criteria andCreateTimeEqualTo(Date value) {
            addCriterion("create_time =", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotEqualTo(Date value) {
            addCriterion("create_time <>", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThan(Date value) {
            addCriterion("create_time >", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("create_time >=", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThan(Date value) {
            addCriterion("create_time <", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThanOrEqualTo(Date value) {
            addCriterion("create_time <=", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIn(List<Date> values) {
            addCriterion("create_time in", values, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotIn(List<Date> values) {
            addCriterion("create_time not in", values, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeBetween(Date value1, Date value2) {
            addCriterion("create_time between", value1, value2, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotBetween(Date value1, Date value2) {
            addCriterion("create_time not between", value1, value2, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateOperIdIsNull() {
            addCriterion("create_oper_id is null");
            return (Criteria) this;
        }

        public Criteria andCreateOperIdIsNotNull() {
            addCriterion("create_oper_id is not null");
            return (Criteria) this;
        }

        public Criteria andCreateOperIdEqualTo(Long value) {
            addCriterion("create_oper_id =", value, "createOperId");
            return (Criteria) this;
        }

        public Criteria andCreateOperIdNotEqualTo(Long value) {
            addCriterion("create_oper_id <>", value, "createOperId");
            return (Criteria) this;
        }

        public Criteria andCreateOperIdGreaterThan(Long value) {
            addCriterion("create_oper_id >", value, "createOperId");
            return (Criteria) this;
        }

        public Criteria andCreateOperIdGreaterThanOrEqualTo(Long value) {
            addCriterion("create_oper_id >=", value, "createOperId");
            return (Criteria) this;
        }

        public Criteria andCreateOperIdLessThan(Long value) {
            addCriterion("create_oper_id <", value, "createOperId");
            return (Criteria) this;
        }

        public Criteria andCreateOperIdLessThanOrEqualTo(Long value) {
            addCriterion("create_oper_id <=", value, "createOperId");
            return (Criteria) this;
        }

        public Criteria andCreateOperIdIn(List<Long> values) {
            addCriterion("create_oper_id in", values, "createOperId");
            return (Criteria) this;
        }

        public Criteria andCreateOperIdNotIn(List<Long> values) {
            addCriterion("create_oper_id not in", values, "createOperId");
            return (Criteria) this;
        }

        public Criteria andCreateOperIdBetween(Long value1, Long value2) {
            addCriterion("create_oper_id between", value1, value2, "createOperId");
            return (Criteria) this;
        }

        public Criteria andCreateOperIdNotBetween(Long value1, Long value2) {
            addCriterion("create_oper_id not between", value1, value2, "createOperId");
            return (Criteria) this;
        }

        public Criteria andCreateOperNameIsNull() {
            addCriterion("create_oper_name is null");
            return (Criteria) this;
        }

        public Criteria andCreateOperNameIsNotNull() {
            addCriterion("create_oper_name is not null");
            return (Criteria) this;
        }

        public Criteria andCreateOperNameEqualTo(String value) {
            addCriterion("create_oper_name =", value, "createOperName");
            return (Criteria) this;
        }

        public Criteria andCreateOperNameNotEqualTo(String value) {
            addCriterion("create_oper_name <>", value, "createOperName");
            return (Criteria) this;
        }

        public Criteria andCreateOperNameGreaterThan(String value) {
            addCriterion("create_oper_name >", value, "createOperName");
            return (Criteria) this;
        }

        public Criteria andCreateOperNameGreaterThanOrEqualTo(String value) {
            addCriterion("create_oper_name >=", value, "createOperName");
            return (Criteria) this;
        }

        public Criteria andCreateOperNameLessThan(String value) {
            addCriterion("create_oper_name <", value, "createOperName");
            return (Criteria) this;
        }

        public Criteria andCreateOperNameLessThanOrEqualTo(String value) {
            addCriterion("create_oper_name <=", value, "createOperName");
            return (Criteria) this;
        }

        public Criteria andCreateOperNameLike(String value) {
            addCriterion("create_oper_name like", value, "createOperName");
            return (Criteria) this;
        }

        public Criteria andCreateOperNameNotLike(String value) {
            addCriterion("create_oper_name not like", value, "createOperName");
            return (Criteria) this;
        }

        public Criteria andCreateOperNameIn(List<String> values) {
            addCriterion("create_oper_name in", values, "createOperName");
            return (Criteria) this;
        }

        public Criteria andCreateOperNameNotIn(List<String> values) {
            addCriterion("create_oper_name not in", values, "createOperName");
            return (Criteria) this;
        }

        public Criteria andCreateOperNameBetween(String value1, String value2) {
            addCriterion("create_oper_name between", value1, value2, "createOperName");
            return (Criteria) this;
        }

        public Criteria andCreateOperNameNotBetween(String value1, String value2) {
            addCriterion("create_oper_name not between", value1, value2, "createOperName");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIsNull() {
            addCriterion("update_time is null");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIsNotNull() {
            addCriterion("update_time is not null");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeEqualTo(Date value) {
            addCriterion("update_time =", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotEqualTo(Date value) {
            addCriterion("update_time <>", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeGreaterThan(Date value) {
            addCriterion("update_time >", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("update_time >=", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeLessThan(Date value) {
            addCriterion("update_time <", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeLessThanOrEqualTo(Date value) {
            addCriterion("update_time <=", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIn(List<Date> values) {
            addCriterion("update_time in", values, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotIn(List<Date> values) {
            addCriterion("update_time not in", values, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeBetween(Date value1, Date value2) {
            addCriterion("update_time between", value1, value2, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotBetween(Date value1, Date value2) {
            addCriterion("update_time not between", value1, value2, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateOperIdIsNull() {
            addCriterion("update_oper_id is null");
            return (Criteria) this;
        }

        public Criteria andUpdateOperIdIsNotNull() {
            addCriterion("update_oper_id is not null");
            return (Criteria) this;
        }

        public Criteria andUpdateOperIdEqualTo(Long value) {
            addCriterion("update_oper_id =", value, "updateOperId");
            return (Criteria) this;
        }

        public Criteria andUpdateOperIdNotEqualTo(Long value) {
            addCriterion("update_oper_id <>", value, "updateOperId");
            return (Criteria) this;
        }

        public Criteria andUpdateOperIdGreaterThan(Long value) {
            addCriterion("update_oper_id >", value, "updateOperId");
            return (Criteria) this;
        }

        public Criteria andUpdateOperIdGreaterThanOrEqualTo(Long value) {
            addCriterion("update_oper_id >=", value, "updateOperId");
            return (Criteria) this;
        }

        public Criteria andUpdateOperIdLessThan(Long value) {
            addCriterion("update_oper_id <", value, "updateOperId");
            return (Criteria) this;
        }

        public Criteria andUpdateOperIdLessThanOrEqualTo(Long value) {
            addCriterion("update_oper_id <=", value, "updateOperId");
            return (Criteria) this;
        }

        public Criteria andUpdateOperIdIn(List<Long> values) {
            addCriterion("update_oper_id in", values, "updateOperId");
            return (Criteria) this;
        }

        public Criteria andUpdateOperIdNotIn(List<Long> values) {
            addCriterion("update_oper_id not in", values, "updateOperId");
            return (Criteria) this;
        }

        public Criteria andUpdateOperIdBetween(Long value1, Long value2) {
            addCriterion("update_oper_id between", value1, value2, "updateOperId");
            return (Criteria) this;
        }

        public Criteria andUpdateOperIdNotBetween(Long value1, Long value2) {
            addCriterion("update_oper_id not between", value1, value2, "updateOperId");
            return (Criteria) this;
        }

        public Criteria andUpdateOperNameIsNull() {
            addCriterion("update_oper_name is null");
            return (Criteria) this;
        }

        public Criteria andUpdateOperNameIsNotNull() {
            addCriterion("update_oper_name is not null");
            return (Criteria) this;
        }

        public Criteria andUpdateOperNameEqualTo(String value) {
            addCriterion("update_oper_name =", value, "updateOperName");
            return (Criteria) this;
        }

        public Criteria andUpdateOperNameNotEqualTo(String value) {
            addCriterion("update_oper_name <>", value, "updateOperName");
            return (Criteria) this;
        }

        public Criteria andUpdateOperNameGreaterThan(String value) {
            addCriterion("update_oper_name >", value, "updateOperName");
            return (Criteria) this;
        }

        public Criteria andUpdateOperNameGreaterThanOrEqualTo(String value) {
            addCriterion("update_oper_name >=", value, "updateOperName");
            return (Criteria) this;
        }

        public Criteria andUpdateOperNameLessThan(String value) {
            addCriterion("update_oper_name <", value, "updateOperName");
            return (Criteria) this;
        }

        public Criteria andUpdateOperNameLessThanOrEqualTo(String value) {
            addCriterion("update_oper_name <=", value, "updateOperName");
            return (Criteria) this;
        }

        public Criteria andUpdateOperNameLike(String value) {
            addCriterion("update_oper_name like", value, "updateOperName");
            return (Criteria) this;
        }

        public Criteria andUpdateOperNameNotLike(String value) {
            addCriterion("update_oper_name not like", value, "updateOperName");
            return (Criteria) this;
        }

        public Criteria andUpdateOperNameIn(List<String> values) {
            addCriterion("update_oper_name in", values, "updateOperName");
            return (Criteria) this;
        }

        public Criteria andUpdateOperNameNotIn(List<String> values) {
            addCriterion("update_oper_name not in", values, "updateOperName");
            return (Criteria) this;
        }

        public Criteria andUpdateOperNameBetween(String value1, String value2) {
            addCriterion("update_oper_name between", value1, value2, "updateOperName");
            return (Criteria) this;
        }

        public Criteria andUpdateOperNameNotBetween(String value1, String value2) {
            addCriterion("update_oper_name not between", value1, value2, "updateOperName");
            return (Criteria) this;
        }
    }

    /**
     * This class was generated by MyBatis Generator.
     * This class corresponds to the database table mtc_assessment_whitelist
     *
     * @mbggenerated do_not_delete_during_merge Fri Oct 10 09:43:51 CST 2025
     */
    public static class Criteria extends GeneratedCriteria {

        protected Criteria() {
            super();
        }
    }

    /**
     * This class was generated by MyBatis Generator.
     * This class corresponds to the database table mtc_assessment_whitelist
     *
     * @mbggenerated Fri Oct 10 09:43:51 CST 2025
     */
    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}
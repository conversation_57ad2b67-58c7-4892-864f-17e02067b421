package com.extracme.evcard.mtc.rest;

import com.extracme.evcard.mtc.dto.MtcApprovalConfigDTO;
import com.extracme.evcard.mtc.service.IMtcApprovalConfigService;
import com.extracme.framework.core.dto.DefaultServiceRespDTO;
import com.extracme.framework.core.vo.DefaultWebRespVO;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;

import java.util.List;

/**
 * <AUTHOR>
 */
@Controller
@ResponseBody
@RequestMapping(value = "api/config")
public class MtcApprovalConfigController {

	@Resource
	private IMtcApprovalConfigService mtcApprovalConfigService;

	/**
	 * 保存
	 * 
	 * @param dtos
	 * @return
	 */
	@RequestMapping(value = "/save", method = RequestMethod.POST)
	public DefaultWebRespVO save(@RequestBody List<MtcApprovalConfigDTO> dtos, HttpServletRequest request) {
		DefaultServiceRespDTO resp = mtcApprovalConfigService.save(dtos, request);
		DefaultWebRespVO vo = new DefaultWebRespVO();
		vo.setCode(String.valueOf(resp.getCode()));
		vo.setMessage(resp.getMessage());
		return vo;
	}

	/**
	 * 查询
	 * 
	 * @return
	 */
	@RequestMapping(value = "/queryList", method = RequestMethod.GET)
	public DefaultWebRespVO queryList() {
		List<MtcApprovalConfigDTO> list = mtcApprovalConfigService.queryList();
		return DefaultWebRespVO.getSuccessVO(list);
	}
}
